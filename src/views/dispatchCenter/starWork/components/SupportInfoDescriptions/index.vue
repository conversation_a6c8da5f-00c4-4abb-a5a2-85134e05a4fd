<template>
  <a-descriptions style="margin-top: 10px;" bordered size="small" :column="2">
    <!-- 支撑方信息 -->
    <a-descriptions-item label="支撑方" :span="2">
      {{ supportInfo.company }}&nbsp;&nbsp;{{ supportInfo.contactUserName }}&nbsp;&nbsp;{{ supportInfo.contactPhone }}
    </a-descriptions-item>

    <!-- 是否支撑 -->
    <a-descriptions-item label="是否支撑">
      {{ formatSupportStatus(supportInfo) }}
    </a-descriptions-item>

    <!-- 支撑人天 - 仅在同意支撑时显示 -->
    <a-descriptions-item v-if="supportInfo.dealType === '1'" label="支撑人天">
      {{ supportInfo.allParseData?.supportCos || '-' }}
    </a-descriptions-item>

    <!-- 实际支撑结果 - 仅在同意支撑时显示 -->
    <a-descriptions-item v-if="supportInfo.dealType === '1'" label="实际支撑结果">
      {{ supportInfo.allParseData?.suggest || '-' }}
    </a-descriptions-item>

    <!-- 回复时间 -->
    <a-descriptions-item label="回复时间">
      {{ supportInfo.dealTime || '-' }}
    </a-descriptions-item>

    <!-- 拒绝原因 - 仅在拒绝或超时拒绝时显示 -->
    <a-descriptions-item v-if="supportInfo.dealType === '2' || supportInfo.dealType === '3'" label="拒绝原因" :span="2">
      {{ formatRejectReason(supportInfo) }}
    </a-descriptions-item>

    <!-- 附件 - 仅在同意支撑且有附件时显示 -->
    <a-descriptions-item v-if="supportInfo.dealType === '1' && hasAttachments" label="附件" :span="2">
      <div class="file-list">
        <div class="flex">
          <p>
            <span>
              <i class="iconfont icon-annex"></i>
              <span>&nbsp;{{ firstAttachment?.name }}&nbsp;</span>
            </span>
          </p>
          <div class="font_0c70eb">
            <span @click="handlePreview(firstAttachment)">&nbsp;预览</span>
            <span @click="handleDownload(firstAttachment)">&nbsp;下载</span>
          </div>
        </div>
      </div>
    </a-descriptions-item>

    <!-- 支撑满意度 - 仅对生态厂商且已支撑时显示 -->
    <a-descriptions-item v-if="showSatisfactionScore">
      <template #label>
        <div style="display: flex;">
          <span>支撑满意度</span>
          <el-tooltip effect="dark" content="请对售前支撑整体满意度打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）" placement="top">
            <div>
              <el-icon :size="12">
                <QuestionFilled />
              </el-icon>
            </div>
          </el-tooltip>
        </div>
      </template>
      <template v-if="canEditScore">
        <el-input-number v-if="isEditMode && !supportInfo.scored" v-model="localSatisfiedScore" :min="0.1" :max="10"
          :step="1" @change="handleScoreChange('satisfiedScore', $event)" />
        <span v-else>{{ supportInfo.satisfiedScore || '-' }}</span>
      </template>
    </a-descriptions-item>

    <!-- 售前响应及时率 - 仅对生态厂商且已支撑时显示 -->
    <a-descriptions-item v-if="showResponseScore">
      <template #label>
        <div style="display: flex;">
          <span>售前响应及时率</span>
          <el-tooltip effect="dark" content="请对售前支撑响应及时率的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
            placement="top">
            <div>
              <el-icon :size="12">
                <QuestionFilled />
              </el-icon>
            </div>
          </el-tooltip>
        </div>
      </template>
      <template v-if="canEditScore">
        <el-input-number v-if="isEditMode && !supportInfo.scored" v-model="localResponseScore" :min="0.1" :max="10"
          :step="1" @change="handleScoreChange('responseScore', $event)" />
        <span v-else>{{ supportInfo.responseScore || '-' }}</span>
      </template>
    </a-descriptions-item>

    <!-- 生态评价 - 仅在同意支撑或超时拒绝但已支撑时显示 -->
    <a-descriptions-item v-if="showEcoEvaluation" label="生态评价" :span="2">
      <el-input v-if="isEditMode && !supportInfo.scored" v-model="localSuggest" type="textarea" placeholder="请输入生态评价"
        :rows="4" :cols="50" :maxlength="100" :show-word-limit="true" @change="handleEvaluationChange" />
      <span v-else>{{ supportInfo.comment || '-' }}</span>
    </a-descriptions-item>
  </a-descriptions>
</template>

<script>
import { defineComponent, computed, ref, watch } from 'vue';
import { QuestionFilled } from "@element-plus/icons-vue"; // 添加引入QuestionFilled图标

export default defineComponent({
  name: 'SupportInfoDescriptions',
  components: {
    QuestionFilled
  },
  props: {
    // 支撑信息数据
    supportInfo: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 模块状态，用于判断是否可编辑
    moduleStatus: {
      type: String,
      default: ''
    },
  },
  emits: ['preview-file', 'download-file', 'score-change', 'evaluation-change'],
  setup(props, { emit }) {
    // 本地评分状态
    const localSatisfiedScore = ref(props.supportInfo.satisfiedScore);
    const localResponseScore = ref(props.supportInfo.responseScore);
    const localSuggest = ref(props.supportInfo.suggest);

    // 监听props变化，更新本地状态
    watch(() => props.supportInfo.satisfiedScore, (newVal) => {
      localSatisfiedScore.value = newVal;
    });

    watch(() => props.supportInfo.responseScore, (newVal) => {
      localResponseScore.value = newVal;
    });

    watch(() => props.supportInfo.suggest, (newVal) => {
      localSuggest.value = newVal;
    });

    // 计算属性
    const hasAttachments = computed(() => {
      return props.supportInfo.allParseData?.fileListDeal?.length > 0;
    });

    const firstAttachment = computed(() => {
      return props.supportInfo.allParseData?.fileListDeal?.[0];
    });

    const isEditMode = computed(() => {
      return props.moduleStatus === 'writeScore';
    });

    const canEditScore = computed(() => {
      return props.supportInfo.dealType === '1' ||
        (props.supportInfo.dealType === '3' && props.supportInfo.hasSupported);
    });

    const showSatisfactionScore = computed(() => {
      return props.supportInfo.enterpriseId && canEditScore.value;
    });

    const showResponseScore = computed(() => {
      return props.supportInfo.enterpriseId && canEditScore.value;
    });

    const showEcoEvaluation = computed(() => {
      return props.supportInfo.dealType === '1' || props.supportInfo.dealType === '3';
    });

    // 格式化支撑状态
    const formatSupportStatus = (item) => {
      if (!item) return '';
      if (item.dealType === '1') return '同意';
      if (item.dealType === '2') return '拒绝';
      if (item.dealType === '3') return '超时拒绝';
      return '';
    };

    // 格式化拒绝原因
    const formatRejectReason = (item) => {
      if (item.dealContent === '其他') {
        return item.comment || item.dealContent;
      }
      return item.dealContent || '-';
    };

    // 事件处理
    const handlePreview = (file) => {
      emit('preview-file', file);
    };

    const handleDownload = (file) => {
      emit('download-file', file);
    };

    const handleScoreChange = (type, value) => {
      emit('score-change', {
        type,
        value,
        supportInfo: props.supportInfo
      });
    };

    const handleEvaluationChange = () => {
      emit('evaluation-change', {
        value: localSuggest.value,
        supportInfo: props.supportInfo
      });
    };

    return {
      localSatisfiedScore,
      localResponseScore,
      localSuggest,
      hasAttachments,
      firstAttachment,
      isEditMode,
      canEditScore,
      showSatisfactionScore,
      showResponseScore,
      showEcoEvaluation,
      formatSupportStatus,
      formatRejectReason,
      handlePreview,
      handleDownload,
      handleScoreChange,
      handleEvaluationChange
    };
  }
});
</script>

<style lang="scss" scoped>
.file-list {
  width: 40% !important;
  cursor: pointer;

  p {
    position: relative;
    padding: 2px 24px 2px 24px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 8px;
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px;
  }

  .iconSize {
    width: 15px;
    height: 15px;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 7px;
  }

  .icon-annex {
    width: 13px;
    height: 13px;
    color: #0c70eb;
    position: absolute;
    top: 2px;
    left: 8px;
  }
}

.font_0c70eb {
  color: #0c70eb;
  cursor: pointer;

  span {
    &:hover {
      text-decoration: underline;
    }
  }
}

:deep(.ant-descriptions-item-label) {
  width: 146px !important;
  min-width: 146px !important;
}
</style>