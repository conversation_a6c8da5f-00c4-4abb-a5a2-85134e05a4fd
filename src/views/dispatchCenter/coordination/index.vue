<template>
  <div style="display: flex;justify-content: center;margin-top: 20px;">
    <div id="addAbilit" class="background_fff" style="width:1200px">
      <div>
        <div class="loading-overlay" v-if="viewLoading">
          <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
        </div>
        <div class="loading-overlay" v-if="formLoading">
          <a-spin :spinning="formLoading" tip="数据加载中..." />
        </div>
        <div v-if="action === 'edit'" class="form-title-class">发起售中调度支撑</div>
        <a-form ref="fileFormRef" :model="formData" labelAlign="right" :rules="rules" :colon="false"
          class="operation padding_l_24 padding_t_24">
          <p class="group_title weight500 font_14 font_00060e" v-if="action !== 'edit'">
            <span class="icon"></span>工单信息
          </p>
          <div v-if="action !== 'edit'">
            <a-row>
              <a-col :span="10">
                <a-form-item label="工单标题">
                  {{ formData.title }}
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>基础信息
          </p>
          <div class="abInfo">
            <!-- 项目编码 -->
            <a-row align="middle">
              <a-col :span="24" :md="{ span: 12, offset: 0 }">
                <template v-if="action == 'edit'">
                  <a-form-item label="项目名称" name="projectName" :labelCol="{ flex: '110px' }"
                    :wrapperCol="{ flex: 'auto' }" class="form-item-align">
                    <a-input v-model:value="formData.projectName" placeholder="请输入项目名称" :maxlength="50"
                      style="width:400px;" />
                  </a-form-item>
                </template>
                <template v-else>
                  <a-form-item label="项目名称" :labelCol="{ flex: '110px' }" class="form-item-align">
                    {{ formData.projectName }}
                  </a-form-item>
                </template>
              </a-col>
              <!-- 项目名称 -->
              <a-col :span="24" :md="{ span: 12, offset: 0 }">
                <template v-if="action == 'edit'">
                  <a-form-item label="项目编码" name="projectCode" :labelCol="{ flex: '110px' }"
                    :wrapperCol="{ flex: 'auto' }" class="form-item-align">
                    <a-input v-model:value="formData.projectCode" placeholder="请填写DICT项目管理系统项目编码" type="text"
                      @input="limitLength" style="width:400px;" />
                  </a-form-item>
                </template>
                <template v-else>
                  <a-form-item label="项目编码" :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }"
                    class="form-item-align">
                    {{ dealContent(formData.projectCode) }}
                  </a-form-item>
                </template>
              </a-col>
            </a-row>
            <!-- 计划验收日期 -->
            <a-row align="middle">
              <a-col :span="20" :md="{ span: 12, offset: 0 }">
                <template v-if="action == 'edit'">
                  <a-config-provider :locale="zhCN">
                    <a-form-item label="计划验收日期" name="planAcceptanceDate" :labelCol="{ flex: '110px' }"
                      :wrapperCol="{ flex: 'auto' }" class="form-item-align">
                      <a-date-picker v-model:value="formData.planAcceptanceDate" format="YYYY-MM-DD"
                        :value-format="'YYYY-MM-DD'" style="width:400px;" :disabled-date="disabledDate"
                        :getCalendarContainer="getCalendarContainer()" />
                    </a-form-item>
                  </a-config-provider>
                </template>
                <template v-else>
                  <a-form-item label="计划验收日期" :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }"
                    class="form-item-align">
                    {{ formatDate(formData.planAcceptanceDate) }}
                  </a-form-item>
                </template>
              </a-col>
            </a-row>
          </div>
          <div class="group_title weight500 font_14 font_00060e">
            <div class="flex justify-center">
              <span class="icon"></span>售中调度流程
            </div>
          </div>
          <div v-for="(moduleItem, moduleIndex) in formData.moduleForm" :key="moduleItem.uid">
            <div class="module_title">
              <p class="weight500 font_14 font_00060e" style="margin-bottom:0">建设内容{{ toChinese(moduleIndex + 1) }}</p>
              <el-button v-if="formData.moduleForm.length > 1 && action == 'edit'"
                class="button_btn custom_btn reject_btn" htmlType="button"
                @click="handleDeleteModule(moduleIndex)">删除建设内容</el-button>
            </div>
            <div class="module_group">
              <a-row align="middle">
                <a-col :span="24">
                  <template v-if="action == 'edit'">
                    <a-form-item label="建设内容描述" :name="['moduleForm', moduleIndex, 'projectContent']"
                      :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }" class="form-item-align"
                      :rules="[{ required: moduleIndex == 0 ? true : false, message: '请输入建设内容描述', trigger: 'blur' }]">
                      <a-textarea :rows="7" :showCount="true" :maxlength="500"
                        placeholder="请输入需要生态厂商承建的主要建设内容，限制500个字符。" v-model:value="moduleItem.projectContent" />
                    </a-form-item>
                  </template>
                  <template v-else>
                    <a-form-item label="建设内容描述" :labelCol="{ flex: '110px' }" class="form-item-align">
                      <div style="white-space: pre-line;">{{ dealContent(moduleItem.projectContent) }}</div>
                    </a-form-item>
                  </template>
                </a-col>
              </a-row>
              <!-- 项目金额 -->
              <a-row align="top">
                <a-col :span="24" :md="{ span: 12, offset: 0 }">
                  <template v-if="action == 'edit'">
                    <a-form-item label="项目金额" :name="['moduleForm', moduleIndex, 'projectAmount']"
                      :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }"
                      :rules="[{ type: 'number', required: true, message: '请输入项目金额', trigger: ['blur', 'change', 'input'] }]"
                      class="form-item-align">
                      <div style="display: flex; align-items: center;width:400px;">
                        <a-input-number v-model:value="moduleItem.projectAmount" placeholder="请输入项目金额" :min="0"
                          :max="999999999999.99" :precision="2" style="flex: 1;"
                          :parser="value => value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" />
                        <span style="margin-left: 8px; min-width: 36px">元</span>
                      </div>
                    </a-form-item>
                  </template>
                  <template v-else>
                    <a-form-item label="项目金额" :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }"
                      class="form-item-align">
                      {{ dealContent(moduleItem.projectAmount) }} 元
                    </a-form-item>
                  </template>
                </a-col>
                <!-- 合同编号 -->
                <a-col :span="24" :md="{ span: 12, offset: 0 }">
                  <template v-if="action == 'edit'">
                    <a-form-item label="编号选择" :name="['moduleForm', moduleIndex, 'contractCode']"
                      :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }" :rules="[
                        { required: true, message: '请输入编号', trigger: ['blur', 'change'] },
                        { validator: validateContractCode, message: '请输入英文或数字,限30字', trigger: ['blur', 'change', 'input'] }
                      ]" class="form-item-align">
                      <a-radio-group v-model:value="moduleItem.contractCodeType"
                        style="margin-top: 5px;margin-bottom: 8px; justify-content:left" @change="handleCodeTypeChange"
                        button-style="solid">
                        <a-radio value="1">后项合同编号</a-radio>
                        <a-radio value="2">订单编号</a-radio>
                      </a-radio-group>
                      <a-input v-model:value="moduleItem.contractCode"
                        :placeholder="contractPlaceholder(moduleItem.contractCodeType)" allow-clear
                        @input="(e) => handleInputChange(e, moduleIndex)" :maxlength="30"
                        :disabled="!moduleItem.contractCodeType" style="width:400px;" />
                    </a-form-item>
                  </template>
                  <template v-else>
                    <a-form-item
                      :label="moduleItem.contractCodeType === 1 ? '后项合同编号' : moduleItem.contractCodeType === 2 ? '订单编号' : '合同编号'"
                      :labelCol="{ flex: '110px' }" :wrapperCol="{ flex: 'auto' }" class="form-item-align">
                      {{ dealContent(moduleItem.contractCode) }}
                    </a-form-item>
                  </template>
                </a-col>
              </a-row>
              <a-form-item v-if="action == 'edit'" label="附件">
                <file-upload :customClassName="'custom_btn active_btn'" :fileInfo="{
                  type: 'other',
                  fileList: moduleItem.fileList,
                  index: null,
                  subIndex: null,
                  accept: '.doc,.docx,.ppt,.pptx',
                  required: false,
                  category: '2',
                  multiple: true,
                  acceptLength: 1,
                  size: 150,
                  mark: '仅支持ppt,doc,docx格式的文件上传(文件个数限1个,单个文件限150M）',
                }" @update-file="(fileInfo) => setFileData(fileInfo, moduleItem)"
                  @update-load="viewFileData"></file-upload>
              </a-form-item>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item label="附件">
                    <div class="file-list" v-if="moduleItem.fileList.length != 0">
                      <div class="flex">
                        <p>
                          <span>
                            <i class="iconfont icon-annex"></i>
                            <span>
                              &nbsp;{{ moduleItem.fileList[0]?.name }} &nbsp;</span>
                          </span>
                        </p>
                        <div class="font_0c70eb">
                          <span @click="view(moduleItem.fileList[0])">
                            &nbsp;预览</span>
                          <span @click="download(moduleItem.fileList[0])">
                            &nbsp;下载</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>暂无附件</div>
                  </a-form-item>
                </a-col>
              </a-row>
              <el-table v-if="action == 'edit'" :data="moduleItem.editDataCompany" :empty-text="'暂无数据'"
                :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column prop="company" label="生态能力方">
                  <template #header>
                    <div style="display: flex; justify-content: center; align-items: center;">
                      <span>生态能力方</span>
                      <a-button class="custom_btn active_btn add_btn"
                        @click="addCooperate(moduleItem.editDataCompany[0], moduleIndex)">新增生态厂商</a-button>
                    </div>
                  </template>
                  <template #default="scope">
                    <div class="table-row-wrapper" style="overflow-x: auto;">
                      <div v-for="(companyItem, index) in scope.row.company" :key="index" class="box"
                        style="margin-bottom: 26px; margin-right: 39px">
                        <a-radio-group :value="moduleItem.selectId" style="display: inline-block">
                          <a-radio :value="companyItem.contactPhone"
                            @change="(e) => onCheckChange(e, companyItem, moduleIndex)"
                            :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                          </a-radio>
                        </a-radio-group>
                        <div style="display: inline-block; width: 93%">
                          <div
                            style="width: 100%; display: flex; justify-content: space-between; align-items: center;text-align: left;">
                            <span v-if="companyItem.ecopartnerName" class="company_left company_underline"
                              style="min-width: 25%; max-width: 38%; display: flex"
                              @click="toCompanyDetail(companyItem)">
                              {{ companyItem.ecopartnerName }}
                            </span>
                            <div class="company_right" style="display: inline-flex; align-items: center;">
                              <img width="20px" height="20px" style="margin-bottom: 0;"
                                src="@/assets/images/score.png" />
                              <span>生态评分</span>
                              <span style="color: #FF9C39FF;font-weight: bold;">{{ companyItem.totalScore ||
                                '-' }}</span>
                            </div>
                            <a-select v-model:value="companyItem.contactName"
                              @change="(value) => selectUserCom(value, companyItem)"
                              :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                              <template v-for="(opt, index) in companyItem.contactList" :key="index">
                                <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                  {{ opt.contactName }}
                                </a-select-option>
                              </template>
                            </a-select>
                            <span style="margin-left: 12px" :style="{
                              color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999',
                            }">
                              {{ companyItem.contactPhone }}
                            </span>
                          </div>
                        </div>
                        <div style="text-align: left; padding-left: 6px; margin-top: 9px">
                          <span v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve">
                            <span v-if="companyItem.approve != 1"
                              style="color: red; margin-bottom: 0; margin-left: 12px">
                              生态厂商暂无该生态联系人！
                            </span>
                          </span>
                          <span v-else-if="companyItem.auth == 0">
                            <p style="color: red; margin-bottom: 0; margin-left: 12px">
                              该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                            </p>
                          </span>
                          <span v-else>
                            <p style="color: red; margin-bottom: 0; margin-left: 12px">
                              生态厂商暂无该生态联系人！
                            </p>
                          </span>
                        </div>
                      </div>

                    </div>
                    <div v-if="scope.row.company.length == 0">暂无数据</div>
                  </template>
                </el-table-column>
              </el-table>

              <el-table v-if="action == 'edit'" :data="moduleItem.editDataCompany" :empty-text="'暂无数据'"
                :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column prop="ownPerson" label="自有能力方" align="center">
                  <template #default>
                    <div
                      style="display: flex; justify-content: space-between; align-items: center;text-align: left; padding: 0 20px; gap: 12px;">
                      <a-select placeholder="自有能力方" v-model:value="moduleItem.ownCooperateData.company" allowClear
                        show-search @select="val => ownPartnerChange(val, moduleIndex)"
                        :not-found-content="ownFetching ? undefined : null" :virtual-scroll="{
                          itemHeight: 32,
                          height: 400,
                          remain: 8,
                        }" style="min-width: 220px; flex-shrink: 0;">
                        <template v-for="opt in displayOwnPartnerOptions" :key="opt.name">
                          <a-select-option :value="opt.name">
                            {{ opt.name }}
                          </a-select-option>
                        </template>
                      </a-select>
                      <a-select placeholder="联系人" v-model:value="moduleItem.ownCooperateData.contact" allowClear
                        @change="(value) => selectOwnUser(value, moduleIndex)"
                        style="min-width: 140px; flex-shrink: 0;">
                        <template v-for="opt in moduleItem.ownContanctList" :key="opt.id">
                          <a-select-option :value="opt.id">
                            {{ opt.realName }}
                          </a-select-option>
                        </template>
                      </a-select>
                      <a-input disabled :value="moduleItem.ownCooperateData.phone" placeholder="联系方式"
                        style="width: 140px; flex-shrink: 0;">
                      </a-input>
                      <a-input disabled :value="moduleItem.ownCooperateData.area" placeholder="负责区域"
                        style="width: 140px; flex-shrink: 0;">
                      </a-input>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-table v-if="moduleItem.action == 'selectApply'" :data="moduleItem.tableData1" :empty-text="'暂无数据'"
                :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px"
                class="scrollable-table resize-table-header-line">
                <el-table-column prop="companyData" :label="moduleItem.tableData1.length > 0 && moduleItem.tableData1[0].companyData.isOwned === 1
                  ? '自有能力方'
                  : '生态厂能力方'" width="580">
                  <template #default="scope">
                    <span style="margin-right: 12px; display: inline-block">{{ scope.row.companyData.ecopartnerName
                    }}</span>
                    <span style="margin-right: 12px; display: inline-block">{{ scope.row.companyData.contactName
                    }}</span>
                    <span>{{ scope.row.companyData.contactPhone }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="调度信息是否准确">
                  <template #default>
                    <el-button link type="primary" size="small" htmlType="button"
                      @click="submitCompany(moduleIndex)">是</el-button>
                    <el-button link type="danger" size="small" htmlType="button"
                      @click="refuseApplyCompany(moduleIndex)">否</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div>
                <el-table
                  v-if="moduleItem.action === 'reSelectPage' && moduleItem.reSelectData.length > 0 && isInitiator"
                  :data="moduleItem.reSelectData" class="resize-table-header-line" :empty-text="'暂无数据'"
                  :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                  <el-table-column prop="info" label="自有能力方/生态厂商" width="480">
                    <template #default="scope">
                      {{ scope.row.info.ecopartnerName }}
                      {{ scope.row.info.contactName }}
                      {{ scope.row.info.contactPhone }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="info" label="调度信息是否准确">
                    <template #default="scope">
                      {{ scope.row && scope.row.dealType == '1' ? '是' : scope.row.dealType == '2' ? '否' : '' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="dealContent" :show-overflow-tooltip="true" label="拒绝原因">
                    <template #default="scope">
                      <el-tooltip v-if="scope.row.dealContent && scope.row.dealContent.length > 60"
                        :append-to="targetElement" trigger="hover" :content="scope.row.dealContent" placement="top"
                        popper-class="custom-tooltip">
                        <p class="content_control">
                          {{ scope.row.dealContent }}
                        </p>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="dealTime" label="回复时间" />
                </el-table>
              </div>
              <el-table v-if="moduleItem.action === 'reSelectPage' && moduleItem.reSelectData.length > 0"
                :data="moduleItem.editDataCompany" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column prop="company" label="生态能力方">
                  <template #header>
                    <div style="display: flex; justify-content: center; align-items: center;">
                      <span>生态能力方</span>
                      <a-button class="custom_btn active_btn add_btn"
                        @click="addCooperate(moduleItem.editDataCompany[0], moduleIndex)">新增生态厂商</a-button>
                    </div>
                  </template>
                  <template #default="scope">

                    <div class="table-row-wrapper" style="overflow-x: auto;">
                      <div v-for="(companyItem, index) in scope.row.company" :key="index" class="box"
                        style="margin-bottom: 26px; margin-right: 39px">
                        <a-radio-group :value="moduleItem.selectId" style="display: inline-block">
                          <a-radio :value="companyItem.contactPhone"
                            @change="(e) => onCheckChange(e, companyItem, moduleIndex)"
                            :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                          </a-radio>
                        </a-radio-group>
                        <div style="display: inline-block; width: 93%">
                          <div
                            style="width: 100%;display: flex; justify-content: space-between;text-align: left; align-items: center;">
                            <span v-if="companyItem.ecopartnerName" class="company_left company_underline"
                              style="min-width: 25%; max-width: 38%; display: flex"
                              @click="toCompanyDetail(companyItem)">
                              {{ companyItem.ecopartnerName }}
                            </span>
                            <div class="company_right" style="display: flex; align-items: center;">
                              <img width="20px" height="20px" style="margin-bottom: 0;"
                                src="@/assets/images/score.png" />
                              <span>生态评分</span>
                              <span style="color: #FF9C39FF;font-weight: bold;">{{ companyItem.totalScore ||
                                '-' }}</span>
                            </div>
                            <a-select v-model:value="companyItem.contactName"
                              @change="(value) => selectUserCom(value, companyItem)"
                              :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                              <template v-for="(opt, index) in companyItem.contactList" :key="index">
                                <a-select-option :value="opt.contactName"
                                  :disabled="opt.approve != 1 || moduleItem.rejectCompanyIdlist.some((value) => value.userId == opt.userId)">
                                  {{ opt.contactName }}
                                </a-select-option>
                              </template>
                            </a-select>
                            <span style="margin-left: 12px" :style="{
                              color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999',
                            }">
                              {{ companyItem.contactPhone }}
                            </span>
                          </div>
                        </div>
                        <div style="text-align: left; padding-left: 6px; margin-top: 9px">
                          <span v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve">
                            <span v-if="companyItem.approve != 1"
                              style="color: red; margin-bottom: 0; margin-left: 12px">
                              生态厂商暂无该生态联系人！
                            </span>
                          </span>
                          <span v-else-if="companyItem.auth == 0">
                            <p style="color: red; margin-bottom: 0; margin-left: 12px">
                              该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                            </p>
                          </span>
                          <span v-else>
                            <p style="color: red; margin-bottom: 0; margin-left: 12px">
                              生态厂商暂无该生态联系人！
                            </p>
                          </span>
                        </div>
                      </div>
                    </div>

                    <div v-if="scope.row.company.length == 0">暂无数据</div>
                  </template>
                </el-table-column>
              </el-table>

              <el-table v-if="moduleItem.action === 'reSelectPage' && moduleItem.reSelectData.length > 0"
                :data="moduleItem.editDataCompany" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column prop="ownPerson" label="自有能力方" align="center">
                  <template #default>
                    <div
                      style="display: flex; justify-content: space-between; align-items: center; text-align: left;padding: 0 20px; gap: 12px;">
                      <a-select placeholder="自有能力方" v-model:value="moduleItem.ownCooperateData.company" allowClear
                        show-search @select="val => ownPartnerChange(val, moduleIndex)"
                        :not-found-content="ownFetching ? undefined : null" :virtual-scroll="{
                          itemHeight: 32,
                          height: 400,
                          remain: 8,
                        }" style="min-width: 220px; flex-shrink: 0;">
                        <template v-for="opt in displayOwnPartnerOptions" :key="opt.name">
                          <a-select-option :value="opt.name">
                            {{ opt.name }}
                          </a-select-option>
                        </template>
                      </a-select>
                      <a-select placeholder="自有联系人" v-model:value="moduleItem.ownCooperateData.contact" allowClear
                        @change="(value) => selectOwnUser(value, moduleIndex)"
                        style="min-width: 140px; flex-shrink: 0;">
                        <template v-for="opt in moduleItem.ownContanctList" :key="opt.id">
                          <a-select-option :value="opt.id"
                            :disabled="moduleItem.rejectCompanyIdlist.some(r => r.userId === opt.id)">
                            {{ opt.realName }}
                          </a-select-option>
                        </template>
                      </a-select>
                      <a-input disabled :value="moduleItem.ownCooperateData.phone" placeholder="联系方式"
                        style="width: 140px; flex-shrink: 0;">
                      </a-input>
                      <a-input disabled :value="moduleItem.ownCooperateData.area" placeholder="负责区域"
                        style="width: 140px; flex-shrink: 0;">
                      </a-input>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div
                v-if="moduleItem.textList && moduleItem.textList.length > 0 && moduleItem.action != 'edit' && moduleItem.action != 'selectApply' && moduleItem.action != 'reSelectPage'">

                <el-descriptions :column="2" border size="default" v-for="(item, index) in moduleItem.textList"
                  :key="index" label-width="200px" style="margin-top: 10px; ">

                  <el-descriptions-item :label="isEcologicalCompany(item) ? '生态能力方' : '自有能力方'" :span="1">
                    <div>
                      {{ item && item.company
                      }}&nbsp;&nbsp;{{
                        item && item.contactUserName
                      }}&nbsp;&nbsp;{{ item && item.contactPhone
                      }}
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item min-width="150px" label="调度信息是否准确" :span="1">
                    <div>{{ item &&
                      item.dealType == '1' ? '是' : item.dealType == '2' ? '否' : ''
                    }}</div>
                  </el-descriptions-item>
                  <el-descriptions-item label="拒绝原因" :span="1" v-if="item.dealType && item.dealType == '2'">
                    {{ item && item.dealContent || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="回复时间" :span="1">
                    <div>{{ item && item && item.dealTime }}</div>
                  </el-descriptions-item>

                  <template
                    v-if="!userInfo.roleKeyList.includes('ecologicalPartner') && isEcologicalCompany(item) && item && item.dealType == '1'">
                    <el-descriptions-item label="售中响应及时率" :span="1" style="min-width: 180px;">
                      <template #default>
                        <el-input-number v-if="moduleItem.action === 'writeScore' && !item.scored"
                          v-model="item.responseTimelinessRate" :min="0.1" :max="10" :precision="1"
                          @change="handleChange" />
                        <p v-else>{{ dealScore(item.responseTimelinessRate) }}</p>
                      </template>
                      <template #label>
                        <span>售中响应及时率</span>
                        <el-tooltip effect="dark" content="请对售中响应及时率的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <el-icon :size="14" style="margin-left: 6px;">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </template>
                    </el-descriptions-item>

                    <el-descriptions-item label="质量、安全保障与规范性" :span="1">
                      <template #default>
                        <el-input-number v-if="moduleItem.action === 'writeScore' && !item.scored"
                          v-model="item.securityStandardization" :min="0.1" :max="10" :precision="1"
                          @change="handleChange" />
                        <p v-else>{{ dealScore(item.securityStandardization) }}</p>
                      </template>
                      <template #label>
                        <span>质量、安全保障与规范性</span>
                        <el-tooltip effect="dark" content="请对实施安全规范的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <el-icon :size="14" style="margin-left: 6px;">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="工期保障" :span="1">
                      <template #default>
                        <div v-if="moduleItem.action === 'writeScore' && !item.scored">
                          <a-select placeholder="请选择工期保障" v-model:value="item.durationGuarantee" allowClear>
                            <a-select-option v-for="(opt, index) in scoreList" :key="index" :value="opt.id">
                              {{ opt.value }}
                            </a-select-option>
                          </a-select>
                        </div>
                        <p v-else>{{ dealScoreNew(item.durationGuarantee) }}</p>
                      </template>
                      <template #label>
                        <span>工期保障</span>
                        <el-tooltip effect="dark" content="请确认本项目是否如期交付：A、如期交付 B 延期但获得客户认可  C 延期且带来客户满意度下降"
                          placement="top">
                          <el-icon :size="14" style="margin-left: 6px;">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </template>
                    </el-descriptions-item>

                    <el-descriptions-item label="交付满意度" :span="1">
                      <template #default>
                        <el-input-number v-if="moduleItem.action === 'writeScore' && !item.scored"
                          v-model="item.deliverySatisfaction" :min="0.1" :max="10" :precision="1"
                          @change="handleChange" />
                        <p v-else>{{ dealScore(item.deliverySatisfaction) }}</p>
                      </template>
                      <template #label>
                        <span>交付满意度</span>
                        <el-tooltip effect="dark" content="请对项目售中交付整体满意度打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <el-icon :size="14" style="margin-left: 6px;">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="能力协同满意度" :span="1">
                      <template #default>
                        <el-input-number v-if="moduleItem.action === 'writeScore' && !item.scored"
                          v-model="item.abilityCollaboration" :min="0.1" :max="10" :precision="1"
                          @change="handleChange" />
                        <p v-else>{{ dealScore(item.abilityCollaboration) }}</p>
                      </template>
                      <template #label>
                        <span>能力协同满意度</span>
                        <el-tooltip effect="dark" content="请填写本次交付实施中融自有能力配合度情况，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <el-icon :size="14" style="margin-left: 6px;">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </template>
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="!userInfo.roleKeyList.includes('ecologicalPartner') && item && item.dealType == '1'"
                      label="生态评价" :span="2">
                      <template #default>
                        <div v-if="moduleItem.action === 'writeScore' && !item.scored">
                          <a-textarea v-model:value="item.comment" placeholder="请输入对该生态能力方的支撑评价" :rows="4"
                            :showCount="true" :maxlength="100" />
                        </div>
                        <div v-else style="white-space: pre-wrap; color: #606266; min-height: 100px;">
                          {{ item &&
                            item.comment }}
                        </div>
                      </template>
                    </el-descriptions-item>
                  </template>

                </el-descriptions>
              </div>
              <div class="flex just-center margin_t_32 margin_b_24"
                v-if="moduleItem.action != 'selectApply' && action != 'detail' && action != 'edit'">
                <a-button class="custom_btn active_btn"
                  v-if="moduleItem.action == 'writeScore' && moduleItem.textList[0].dealType == '1'"
                  @click="submitBtn(moduleIndex)" :loading="addLoading">评分</a-button>
                <a-button v-else-if="moduleItem.action == 'reSelectPage' && moduleItem.reSelectData.length > 0"
                  class="custom_btn active_btn" @click="reSubmit(moduleIndex)" :loading="addLoading">确定</a-button>
              </div>
            </div>
          </div>
          <div v-if="action == 'edit'" style="width: 100%;text-align: center;margin: 20px 0;">
            <a-button class="custom_btn active_btn" style="border: #0c70eb 1px solid;"
              @click="addModule">新增建设内容</a-button>
          </div>
          <!-- 工单流程 -->
          <p class="group_title weight500 font_14 font_00060e" v-if="action != 'edit'">
            <span class="icon"></span>工单流程
          </p>
          <div style="padding: 0 24px">
            <el-table v-if="action != 'edit'" class="resize-table-header-line" :data="tableDataWork"
              :empty-text="'暂无数据'" border :header-cell-style="{ textAlign: 'center' }"
              style="width: 100%; margin-top: 20px">
              <el-table-column align="center" prop="activityName" label="工单流程" width="180" />
              <el-table-column align="center" prop="assigneeName" label="处理人" />
              <el-table-column align="left" prop="dealContent" label="处理内容">
                <template #default="scope">
                  <el-tooltip :append-to="targetElement" trigger="hover" popper-class="custom-tooltip"
                    :content="scope.row.dealContent" placement="top" v-if="
                      scope.row.dealContent && scope.row.dealContent.length > 60
                    ">
                    <p v-if="scope.row.dealContent !== ''" class="content_control">
                      {{ scope.row.dealContent }}
                    </p>
                  </el-tooltip>
                  <p v-if="scope.row.dealContent == ''">-</p>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="endTime" label="处理时间" />
            </el-table>
          </div>
        </a-form>
      </div>
      <div class="flex just-center margin_t_32 margin_b_24">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="cancel" v-if="action !== 'submitPage'">返回</a-button>
        <a-button v-if="action == 'edit'" class="custom_btn active_btn" @click="createOrder"
          :loading="addLoading">确定</a-button>
      </div>
      <div class="flex just-center margin_t_32" v-if="action == 'dealedAll'">
        <a-button class="margin_r_10" @click="cancel">关闭</a-button>
      </div>
    </div>
    <a-modal v-model:visible="showSuggest" :title="companyAload ? '调度信息确认' : '拒绝理由'" @ok="handleOkSugget" :footer="null"
      @close="handleCloseSugget" width="40%">
      <a-form ref="dealFormRef" :model="formDataDeal" :rules="dealRules" labelAlign="right" :colon="false"
        class="operation">
        <!-- 拒绝时显示拒绝原因 -->
        <a-form-item v-if="!companyAload" label="拒绝原因" name="suggest">
          <a-textarea v-model:value="formDataDeal.suggest" rows="" :maxlength="100" showCount
            placeholder="请输入拒绝原因，限制100个字符"></a-textarea>
        </a-form-item>
      </a-form>

      <div style="text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseSugget">取消</a-button>
        <a-button class="custom_btn active_btn" @click="handleOkSugget">确认</a-button>
      </div>
    </a-modal>

    <a-modal v-model:visible="showAdd" title="生态厂商新增" :footer="null" @close="closeAdd" width="50%">
      <a-form ref="addFormRef" :model="CooperateData" labelAlign="right" :rules="addRules" :colon="false">
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态合作方" name="company">
              <a-select placeholder="请选择生态合作方" v-model:value="CooperateData.company" allowClear show-search
                @select="ecologyChangeOld" :not-found-content="fetching ? undefined : null" :virtual-scroll="{
                  itemHeight: 32,
                  height: 400,
                  remain: 8,
                }">
                <template #notFoundContent>
                  <div style="text-align: center;">
                    <a-spin size="small" />
                    <span style="margin-left: 8px">加载中...</span>
                  </div>
                </template>
                <template v-for="opt in displayOptions" :key="opt.name">
                  <a-select-option :value="opt.name"
                    :disabled="formData.moduleForm[currentModuleIndex]?.companyIdList.includes(opt.enterpriseId)">
                    {{ opt.name }}
                  </a-select-option>
                </template>
              </a-select>
              <div v-if="CooperateData.company">
                <p v-if="CooperateData.sync != 1 || CooperateData.auth != 1"
                  style="color: red; margin-bottom: 0; margin-left: 12px">
                  该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                </p>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系人" name="contanct">
              <a-select placeholder="请选择生态联系人" v-model:value="CooperateData.contanct" allowClear
                @change="(value) => selectUser(value)">
                <template v-for="(opt, index) in contanctList" :key="index">
                  <a-select-option :value="opt.contactName" :disabled="opt.approve != 1 || !formData.moduleForm[currentModuleIndex] ||
                    formData.moduleForm[currentModuleIndex].rejectCompanyIdlist?.some(r => r.userId === opt.id)">
                    {{ opt.contactName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系方式">
              <a-input disabled :value="CooperateData.phone" placeholder="请输入生态联系方式">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="负责区域">
              <a-input disabled :value="CooperateData.area" placeholder="请选择负责区域">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center margin_t_32">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="closeAdd">取消</a-button>
        <a-button class="custom_btn active_btn" @click="submitAdd" :loading="addLoading"
          :disabled="isSyncAuth">提交</a-button>
      </div>
    </a-modal>
  </div>

</template>
<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import { QuestionFilled } from "@element-plus/icons-vue"; // 添加引入QuestionFilled图标
import {
  batchSubmitWithdrawProcess,
} from "@/api/processManage/backlog&completed/index.js";
import {
  submitOrder,
  selectOrderById,
  operateOrderById,
} from "@/api/processManage/index.js";
import { getOwnerUserList } from "@/api/dispatchCenter/backlog&completed.js";
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import 'moment/dist/locale/zh-cn';
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { selectTree } from "@/api/system/team";
import { sortTextList } from '@/utils/index.js';
import {
  beginSend,
} from "@/api/ticket/ticket.js";
export default defineComponent({
  components: {
    QuestionFilled, // 注册QuestionFilled组件
  },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const getUserInfo = JSON.parse(window.localStorage.getItem("userInfo"));
    const data = reactive({
      isInitiator: false, //是否发起人
      operateIndex: 0,
      formData: {
        fileList: [],
        projectName: "",
        title: "",
        projectCode: "", // 添加projectCode字段
        planAcceptanceDate: "", // 添加planAcceptanceDate字段
        updateTime: "",
        ecopartnerName: "",
        ecologicalCapacityName: "",
        ecologicalCapacityPhone: "",
        moduleForm: [{
          uid: Date.now() + Math.random(),
          textList: [], // 生态厂商列表
          tableData1: [],
          projectContent: "", // 建设内容描述
          projectAmount: undefined, // 项目金额
          contractCodeType: '1',
          contractCode: "",// 合同编号或者订单号
          fileList: [],
          ecopartnerName: "",
          ecologicalCapacityName: "",
          ecologicalCapacityPhone: "",
          editDataCompany: [{
            company: [], // 生态厂商
          }],
          ipartnerId: undefined,
          selectCompanyList: {},
          selectCompanyObj: {},// 合作方或自由方对象
          selectPhone: "",
          companyIdList: [], //生态合作方中auth并sync的的公司id的list
          contanctList: [],
          CooperateData: {
            company: undefined,
            contact: undefined,
            phone: undefined,
            area: undefined,
            auth: undefined,
            sync: undefined,
          },
          ownContanctList: [],// 自由方用户list
          ownCooperateData: {
            company: undefined,
            contact: undefined,
            phone: undefined,
            area: undefined,
          },
          reSelectData: [], //厂商拒绝后调度人页面重新选择厂商数据
          reDispatch: false, // 重新调度
          selectId: "",
          rejectCompanyIdlist: [], //拒绝的厂商id
          action: '',
        }]
      },
      CooperateData: {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        auth: undefined,
        sync: undefined,
        enterpriseId: undefined,
        projectCode: undefined,
      },
      userInfo: getUserInfo,
      viewLoading: false,
      formLoading: false,
      scoreList: [
        {
          id: "1",
          value: "A",
        },
        {
          id: "0.5",
          value: "B",
        },
        {
          id: "-1",
          value: "C",
        },
      ],
      selectCompanyObj: {},// 合作方或自由方对象
      ipartnerId: undefined, // 能力方id
      contanctList: [],
      ownContanctList: [],// 自由方用户list
      action: Route.query.action,
      addLoading: false,
      rules: {
        title: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        projectCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        planAcceptanceDate: [
          { required: true, message: "请选择验收日期", trigger: "change" },
        ],
      },
      tableDataWork: [],
      type: "", //1方案2场景3能力
      suggest: "", //处理意见
      formDataDeal: {
        suggest: "",
      },
      showSuggest: false, //是否显示处理意见弹框
      dealRules: { suggest: [{}] },
      companyAload: true,
      teamOldList: [],
      editDataCompany: [{
        company: [], // 生态厂商
      }],
      selectPhone: "", //工单发起页面选择人
      refuseApplySecond: false, //厂商拒绝后调度人页面重新选择厂商数据
      definitionId: "",//售中工单id
      dataCompanyNew: [{
        company: [], // 生态厂商
      }],
      showAdd: false,
      isSyncAuth: false,
      companyId: "",
      currentModuleIndex: 0,
      addRules: {
        company: [
          {
            required: true,
            message: "请选择生态合作方",
            trigger: "change",
          },
        ],
        contanct: [
          {
            required: true,
            message: "请选择联系人",
            trigger: "change",
          },
        ],
      },
    });
    const fileFormRef = ref(null);
    const addFormRef = ref(null);
    const dealFormRef = ref(null);
    const displayOptions = ref([]); // 显示的数据
    const displayOwnPartnerOptions = ref([]); // 自有显示的数据
    const fetching = ref(false);
    const ownFetching = ref(false);
    const toChinese = (num) => {
      const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
      if (num <= 10) return chineseNumbers[num];
      if (num < 20) return `十${chineseNumbers[num - 10]}`;
      return num.toString();
    }
    const handleInputChange = (e, index) => {
      let value = e.target.value;
      value = value.replace(/[^a-zA-Z0-9]/g, '');
      if (value.length > 30) {
        value = value.slice(0, 30);
      }

      data.formData.moduleForm[index].contractCode = value;
      nextTick(() => {
        fileFormRef.value.validateFields([
          ['moduleForm', index, 'contractCode']
        ]);
      });
    };
    const limitLength = (e) => {
      let value = e.target.value;
      value = value.replace(/[^a-zA-Z0-9]/g, '');
      if (value.length > 30) {
        value = value.slice(0, 30);
      }
      data.formData.projectCode = value;
    };
    const createOrder = async () => {
      // 表单验证
      await fileFormRef.value?.validate();
      data.addLoading = true;
      // 检查必填字段 - 使用修改后的字段名
      if (!data.formData.projectCode) {
        message.error("请输入项目编码");
        data.addLoading = false;
        return;
      }
      if (!data.formData.projectName) {
        message.error("请输入项目名称");
        data.addLoading = false;
        return;
      }
      if (!data.formData.planAcceptanceDate) {
        message.error("请选择验收日期");
        data.addLoading = false;
        return;
      }
      const planAcceptanceDate = data.formData.planAcceptanceDate
        ? typeof data.formData.planAcceptanceDate === "object" &&
          data.formData.planAcceptanceDate.format
          ? data.formData.planAcceptanceDate.format("YYYY-MM-DD")
          : data.formData.planAcceptanceDate
        : "";
      for (let index = 0; index < data.formData.moduleForm.length; index++) {
        const element = data.formData.moduleForm[index];
        if (
          (!element.selectCompanyList || !element.selectCompanyList.userId || element.selectCompanyList.userId === "")
        ) {
          message.error(`请为建设内容${index + 1}选择生态能力方或自有能力方`);
          data.addLoading = false;
          return;
        }
      }
      // 准备提交的数据结构：多个模块
      const postData = data.formData.moduleForm.map(module => {
        return {
          title: `关于${data.formData.projectName}项目的售中支撑工单`, // 工单标题
          projectName: data.formData.projectName, // 项目名称
          projectCode: data.formData.projectCode, // 项目编码
          planAcceptanceDate: planAcceptanceDate, // 计划验收日期

          projectContent: module.projectContent, // 建设内容描述（支撑模块内字段）
          projectAmount: module.projectAmount !== undefined ? parseFloat(module.projectAmount).toFixed(2) : "0.00", // 项目金额，格式化两位小数

          contractCodeType: module.contractCodeType || "1", // 编号类型选择
          contractCode: module.contractCode || "", // 合同编号或订单号
          fileList: (module.fileList || []).map(file => ({
            name: file.name,
            url: file.url,
            path: file.path,
          })),

          // 生态能力方或者自有能力方信息
          ipartnerId: module.ipartnerId, // 生态合作方ID
          ecologicalCapacityBy: module.selectCompanyList?.userId || "", // 能力方id
          stfk: module.selectCompanyList?.userId || "",
          ecopartnerName: module.selectCompanyList?.ecopartnerName || "",
          ecologicalCapacityName: module.selectCompanyList?.contactName || "",
          ecologicalCapacityPhone: module.selectCompanyList?.contactPhone || "",
          selectCompanyList: module.selectCompanyList || {},

          // 其他公共字段
          businessType: 11,  // 固定调度支撑
          workTitle: `关于${data.formData.projectName}项目的售中支撑工单`,
          type: 9, // 售中类型
        };
      });

      //调用统一提交接口
      submitOrder({
        customDataForm: JSON.stringify(postData),
        title: `关于${data.formData.projectName}项目的售中支撑工单`,
        type: 9, // 售中类型
        projectName: data.formData.projectName,
        projectCode: data.formData.projectCode,
        planAcceptanceDate: planAcceptanceDate,
        businessType: 11, // 调度支撑
        comment: "选择能力方",
        procDefId: data.definitionId,
      }).then(res => {
        message.success("提交成功");
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
      }).catch(err => {
        // message.error("提交失败，请稍后重试");
      }).finally(() => {
        data.addLoading = false;
      });
    };
    // 提交按钮
    const reSubmit = async (index) => {
      data.operateIndex = index;
      try {
        data.addLoading = true;
        const operateModule = data.formData.moduleForm[data.operateIndex]
        if (!operateModule.selectCompanyList.userId) {
          message.warning("请选择生态能力方或自有能力方");
          throw new Error("请选择生态能力方或自有能力方");
        }
        if (operateModule.rejectCompanyIdlist.some((value) => value.userId == operateModule.selectCompanyList.userId)) {
          message.warning("请选择生态能力方或自有能力方");
          throw new Error("该能力方联系人已拒绝，请重新选择");
        }
        const originData = operateModule.textList[0]?.allParseData || {};
        const customDataForm = JSON.parse(JSON.stringify(originData));

        customDataForm.selectCompanyList = operateModule.selectCompanyList || {};
        customDataForm.ipartnerId = operateModule.ipartnerId || null;
        customDataForm.stfk = operateModule.selectCompanyList.userId || null;

        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "重新选择能力方",
          variables: {
            customDataForm: JSON.stringify(customDataForm),
            stfk: operateModule.selectCompanyList.userId,
          },
        }];
        batchSubmitWithdrawProcess(postData).then((res) => {
          message.success(res.msg);
          operateOrderById(Route.query.orderId).then((res2) => {
            let tempProInstList = res2.data.procInstList;
            if (tempProInstList && tempProInstList.length > 0) {
              // 该工单仍有待办，操作完成
              reModule();
              getData();
            } else {
              window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
            }
          })
          // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
        }).finally(() => {
          data.addLoading = false;
        });
      } catch (error) {
        console.log(error);
        data.addLoading = false;
      }
    };
    const cancel = () => {
      data.addLoading = false;
      if (data.action == "dealed") {
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=completed";
      }
      if (data.action == "writeScore") {
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
      } else if (data.action == "edit") {
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=waitWork";
      } else {
        Router.go(-1);
      }
      data.addLoading = false;
    };
    const getData = () => {
      // 初始化工作流数据
      initWorkflowData();
      let params = {
        pageNum: 1,
        pageSize: 10,
        processName: "售中调度工单",
      };
      beginSend(params).then((res) => {
        // 获取售前调度工单流程id
        data.definitionId = res.data ? res.data.rows[0].definitionId : "";
      });

      // 添加字段同步辅助函数
      const syncFormFields = createSyncFormFields();

      // 处理coordinationId参数逻辑
      if (Route.query.orderId) {
        // 新售中工单逻辑
        handleOrderIdCase()
        return;
      }
      // 处理不同来源的工单创建逻辑
      handleNewWorkOrderCreation(syncFormFields);
    };

    // ========== 工具函数 ==========

    // 初始化工作流数据
    const initWorkflowData = () => {
      data.tableDataWork = [
        {
          activityName: "提交申请",
          assigneeName: "汪修稷",
          dealContent: "发起售中调度",
          endTime: "2025-04-30 19:28:30",
        },
        {
          activityName: "生态能力方",
          assigneeName: "汪修稷",
          dealContent: "接收工单",
          endTime: "2025-04-30 19:28:30",
        },
        {
          activityName: "交付经理",
          assigneeName: "汪修稷",
          dealContent: "评分",
          endTime: "2025-05-01 00:32:47",
        },
      ];
    };

    // 创建字段同步函数
    const createSyncFormFields = () => {
      const fieldMappings = [
        { old: "projectcode", new: "projectCode" },
        { old: "money", new: "projectAmount" },
        { old: "number", new: "contractCode" },
        { old: "time", new: "planAcceptanceDate" },
      ];

      return () => {
        fieldMappings.forEach(({ old, new: newField }) => {
          if (data.formData[old]) data.formData[newField] = data.formData[old];
          if (data.formData[newField])
            data.formData[old] = data.formData[newField];
        });
      };
    };

    // ========== 主要业务逻辑 ==========
    const handleOrderIdCase = async () => {
      data.formLoading = true;
      // 设置页面状态
      data.action = ["edit", "writeScore", "detail", "selectApply", "reSelectPage"].includes(Route.query.action)
        ? Route.query.action
        : "detail";
      try {
        const apiCall = data.action === "detail" ? selectOrderById : operateOrderById;
        const res = await apiCall(Route.query.orderId);
        // 判断是否是发起人
        data.isInitiator = (res.data.createBy === data.userInfo.id);
        if (res.data.procInstList && res.data.procInstList.length > 0) {
          // 填充基础表单数据
          fillBasicFormData(res.data.procInstList[0]);

          // 解析并填充多模块数据
          fillModuleFormData(res.data.procInstList);

          // 处理流程节点数据
          fillWorkflowData(res.data.historyProcNodeList);
        }
      } catch (error) {
        console.error("获取工单详情异常", error);
        message.error("获取工单详情失败，请稍后重试");
      } finally {
        data.formLoading = false;
      }

    };
    const isEcologicalCompany = (moduleItem) => {
      return moduleItem?.isOwned === 0
    }

    // ========== 工单数据处理 ==========

    // 填充基础表单数据
    const fillBasicFormData = (moduleItem) => {
      const parsedData = JSON.parse(JSON.parse(moduleItem.customDataForm));
      data.formData = { ...data.formData, ...parsedData };
    };
    const fillModuleFormData = (procInstList) => {
      procInstList.forEach((moduleItem, index) => {
        const moduleData = JSON.parse(JSON.parse(moduleItem.customDataForm));

        if (!data.formData.moduleForm[index]) {
          data.formData.moduleForm[index] = createEmptyModule();
        }

        Object.assign(data.formData.moduleForm[index], moduleData);

        data.formData.moduleForm[index].procInstId = moduleItem.procInstId;
        data.formData.moduleForm[index].taskId = moduleItem.tasks?.[0]?.taskId || moduleItem.tasks?.[0]?.id || "";
        data.formData.moduleForm[index].taskName = moduleItem.tasks?.[0]?.taskName || "";
        data.formData.moduleForm[index].projectAmount = Number(moduleItem.projectAmount).toFixed(2) || "";
        if (data.action === 'detail') {
          data.formData.moduleForm[index].action = 'detail';
        } else {
          switch (moduleItem.tasks?.[0]?.taskName) {
            case '生态反馈':
              data.formData.moduleForm[index].action = 'selectApply';
              break;
            case '生态评分':
              data.formData.moduleForm[index].action = 'writeScore';
              break;
            case '提交申请':
              data.formData.moduleForm[index].action = 'reSelectPage';
              break;
            default:
              data.formData.moduleForm[index].action = data.action;
          }
        }
        // 保证附件结构正确
        if (!Array.isArray(data.formData.moduleForm[index].fileList)) {
          data.formData.moduleForm[index].fileList = [];
        }
        handleMidSaleFeedback(moduleItem.midSaleSupportInfos, index, moduleData);
      });
    };
    const handleMidSaleFeedback = (midSaleSupportInfos, moduleIndex, parsedData) => {
      if (!parsedData.fileListDeal) parsedData.fileListDeal = [];

      const allData = midSaleSupportInfos || [];
      const moduleItem = data.formData.moduleForm[moduleIndex];
      // 用于展示所有生态合作方反馈
      const hasScore = (item) => {
        const requiredFields = [
          "responseTimelinessRate",
          "securityStandardization",
          "durationGuarantee",
          "deliverySatisfaction",
          "abilityCollaboration"
        ];
        return requiredFields.some(field => {
          const val = item[field];
          return val !== null && val !== undefined && val !== '';
        });
      }
      const newTextList = allData.map((item) => {
        return {
          ...item,
          enterpriseId: parsedData.selectCompanyList.enterpriseId,
          allParseData: parsedData,
          scored: hasScore(item),
        };
      });

      if (!data.isInitiator) {
        // 查找当前能力方的所有记录
        const currentUserRecords = newTextList.filter(item => item.userId === data.userInfo.id);
        if (currentUserRecords.length > 0) {
          // 有自己的记录，只显示自己的记录
          // moduleItem.textList = currentUserRecords;
          moduleItem.textList = JSON.parse(JSON.stringify(currentUserRecords));
        } else {
          // 没有自己的记录，显示全部
          moduleItem.textList = JSON.parse(JSON.stringify(newTextList));
        }
      } else {
        // 发起人展示全部记录
        moduleItem.textList = JSON.parse(JSON.stringify(newTextList));
      }
      if (moduleItem.action == "writeScore") {
        // 同意的排第一，否则按时间最新拒绝的排序
        moduleItem.textList = sortTextList(moduleItem.textList)
      }
      // 筛选被拒绝的列表，收集拒绝的能力方信息
      const rejectedItems = newTextList.filter(item => item.dealType == "2");

      const moduleRejectList = rejectedItems.map(item => ({
        ecopartnerName: item.company || "自有能力方",
        contactName: item.contactUserName,
        contactPhone: item.contactPhone,
        userId: item.userId,
      }));
      moduleItem.rejectCompanyIdlist = moduleRejectList;
      if (moduleItem.action === 'reSelectPage' && moduleItem.rejectCompanyIdlist.length > 0) {
        moduleItem.editDataCompany = [{
          company: [],
        }]
      }

      // 重新构造reSelectData，只展示所有拒绝的项
      moduleItem.reSelectData = rejectedItems.map(item => ({
        ...item,
        info: {
          ecopartnerName: item.company || (moduleItem.selectCompanyList ? moduleItem.selectCompanyList.ecopartnerName : "自有能力方"),
          contactName: item.contactUserName,
          contactPhone: item.contactPhone,
        },
        dealTime: item.dealTime || '',
        dealContent: item.dealContent || '',
      }));
      // console.log('拒绝的项',moduleItem.reSelectData)

      moduleItem.tableData1 = [];
      allData.forEach(item => {
        if (item.dealType == "0" || item.dealType == null) {
          const companyName = item.company || (moduleItem.selectCompanyList ? moduleItem.selectCompanyList.ecopartnerName : "自有能力方");
          const isOwned = item.isOwned || 0;
          moduleItem.tableData1.push({
            companyData: {
              ecopartnerName: companyName,
              contactName: item.contactUserName,
              contactPhone: item.contactPhone,
              isOwned: isOwned,
            },
          });
        }
      });
    };
    const createEmptyModule = () => ({
      uid: Date.now() + Math.random(),
      textList: [], // 生态厂商列表
      tableData1: [],
      procInstId: "",
      projectContent: "",
      projectAmount: undefined,
      contractCodeType: undefined,
      contractCode: "",
      fileList: [],
      ecopartnerName: "",
      ecologicalCapacityName: "",
      ecologicalCapacityPhone: "",
      selectCompanyObj: {},
      selectPhone: "",
      contanctList: [],
      CooperateData: {},
      ownContanctList: [],
      ownCooperateData: {},
      rejectCompanyIdlist: [],
    });
    const fillWorkflowData = (historyProcNodeList) => {
      if (!Array.isArray(historyProcNodeList)) {
        data.tableDataWork = [];
        return;
      }
      const nodes = JSON.parse(JSON.stringify(historyProcNodeList));
      const filteredNodes = nodes.filter(node => node.activityName !== "结束");
      const completedNodes = [];
      const incompleteNodes = [];

      filteredNodes.forEach(item => {
        if (item.endTime) {
          completedNodes.push(item);
        } else {
          incompleteNodes.push(item);
        }
      });

      completedNodes.sort((a, b) =>
        new Date(a.endTime).getTime() - new Date(b.endTime).getTime()
      );
      incompleteNodes.reverse()

      data.tableDataWork = [...completedNodes, ...incompleteNodes];

      // 移除末尾的特定节点
      const lastNode = data.tableDataWork[data.tableDataWork.length - 1];
      if (lastNode && ["已审核", "结束"].includes(lastNode.activityName)) {
        data.tableDataWork.pop();
      }


      // data.tableDataWork = historyProcNodeList.reverse();
      data.tableDataWork.forEach(item => {
        const orgPart = item.orgName ? `(${item.orgName})` : '';
        const phone = item.phone ? item.phone : '';
        item.assigneeName = `${item.assigneeName || ''}${orgPart}${phone}`;
      });
      // console.log(data.tableDataWork);
      // 过滤末尾常见状态节点
      ["已审核", "结束"].forEach(name => {
        if (data.tableDataWork[data.tableDataWork.length - 1]?.activityName === name) {
          data.tableDataWork.pop();
        }
      });

      // 丰富每节点显示内容
      data.tableDataWork.forEach(item => {
        if (item.commentList && item.commentList.length > 0) {
          item.dealContent = item.commentList[0].fullMessage || "";
          // 根据流程环节名，调整提示内容
          if (item.activityName == "生态反馈" || item.activityName == "能力方反馈") {
            if (item.commentList[0].type == 1) {
              item.dealContent = "同意。" + item.commentList[0].message;
            } else if (item.commentList[0].type == 2) {
              item.dealContent = "拒绝。" + item.commentList[0].message;
            }
          }
        } else {
          item.dealContent = "";
        }

        if (!item.endTime) {
          item.endTime = "-";
        }
      });
    };

    const submitCompany = (moduleIndex) => {
      data.showSuggest = true;
      data.companyAload = true;// 同意
      data.dealRules.suggest = [{ required: false }];
      data.formDataDeal.suggest = '';
      data.currentModuleIndex = moduleIndex;
    };
    const refuseApplyCompany = (moduleIndex) => {
      data.showSuggest = true;
      data.companyAload = false;// 拒绝
      data.dealRules.suggest = [
        { required: true, message: "请输入拒绝原因", trigger: "change" },
      ];
      data.formDataDeal.suggest = '';
      data.currentModuleIndex = moduleIndex;
    };
    // 处理新建工单逻辑
    const handleNewWorkOrderCreation = async (syncFormFields) => {
      const handlers = {
        center: handleCenterOrder,
      };
      const handler = handlers[Route.query.from] || handlers.default;
      await handler(syncFormFields);
    };
    // 处理从调度工作台发起的售中工单
    const handleCenterOrder = async (syncFormFields) => {
      data.editDataCompany = [{},];
      syncFormFields();
    };
    getData();
    // 生态厂商单选事件
    const onCheckChange = (e, item, index) => {
      const { value } = e.target;
      const { action } = data;
      data.formData.moduleForm[index].ownCooperateData = {
        company: undefined,
        contact: undefined,
        phone: undefined,
        area: undefined,
      };
      data.formData.moduleForm[index].ownContanctList = [];
      if (action === "edit") {
        data.formData.moduleForm[index].selectCompanyList = {
          ecopartnerName: item.ecopartnerName || "",     // 生态能力方名
          contactPhone: item.contactPhone || "",
          contactName: item.contactName || "",
          userId: item.userId,
          enterpriseId: item.enterpriseId || "",
          sync: item.sync,                   // 取生态能力方同步状态
          auth: item.auth,                   // 取认证状态
          approve: item.approve,
          isOwned: 0,
        };
        data.formData.moduleForm[index].ipartnerId = item.userId;
        data.formData.moduleForm[index].selectId = value;
      } else {
        data.formData.moduleForm[index].selectCompanyList = {
          ecopartnerName: item.ecopartnerName || "",     // 生态能力方名
          contactPhone: item.contactPhone || "",
          contactName: item.contactName || "",
          userId: item.userId,
          enterpriseId: item.enterpriseId || "",
          sync: item.sync,                   // 取生态能力方同步状态
          auth: item.auth,                   // 取认证状态
          approve: item.approve,
          isOwned: 0,
        };
        data.formData.moduleForm[index].ipartnerId = item.userId;
        data.formData.moduleForm[index].selectId = value;
      }
    };
    const selectEcoUser = (val, moduleIndex) => {
      const moduleItem = data.formData.moduleForm[moduleIndex];
      let info = moduleItem.contanctList || [];
      const result = info.filter((item) => {
        return item.contactName === val;
      });
      if (result.length > 0) {
        const contact = result[0];
        moduleItem.selectCompanyList = {
          ecopartnerName: moduleItem.CooperateData.company || "",     // 生态能力方名
          contactPhone: contact.contactPhone || "",
          contactName: contact.contactName || "",
          userId: contact.userId,
          enterpriseId: contact.enterpriseId || "",
          sync: moduleItem.CooperateData.sync || 0,                   // 取生态能力方同步状态
          auth: moduleItem.CooperateData.auth || 0,                   // 取认证状态
          approve: contact.approve || 0,
          isOwned: 0,
        };
        moduleItem.CooperateData.phone = contact?.contactPhone;
        moduleItem.CooperateData.area = contact?.contactAddress || moduleItem.CooperateData.area;
        moduleItem.selectPhone = contact?.contactPhone;
        moduleItem.selectCompanyObj.userId = contact?.userId;
        // 赋值给最外层字段
        moduleItem.ecopartnerName = moduleItem.CooperateData.company || "";
        moduleItem.ecologicalCapacityName = contact?.contactName || "";
        moduleItem.ecologicalCapacityPhone = contact?.contactPhone || "";
      } else {
        moduleItem.CooperateData.phone = "";
        moduleItem.CooperateData.area = "";
        moduleItem.selectPhone = "";
        moduleItem.selectCompanyObj.userId = "";
        moduleItem.ecopartnerName = "";
        moduleItem.ecologicalCapacityName = "";
        moduleItem.ecologicalCapacityPhone = "";
      }
    };
    const selectOwnUser = (val, moduleIndex) => {
      const moduleItem = data.formData.moduleForm[moduleIndex];
      let info = moduleItem.ownContanctList || [];
      const result = info.filter((item) => {
        return item.id === val;
      });
      if (result.length > 0) {
        moduleItem.selectCompanyList = {
          ecopartnerName: moduleItem.ownCooperateData.company || "",
          contactName: result[0]?.realName || "",
          contactPhone: result[0]?.phone || "",
          contactAddress: moduleItem.ownCooperateData.area || "江苏省",
          approve: 1,
          userId: result[0]?.id,
          isOwned: 1,
        };
        moduleItem.ipartnerId = result[0]?.id;
        moduleItem.ownCooperateData.phone = result[0]?.phone;
        moduleItem.selectPhone = result[0]?.contactPhone;
        moduleItem.selectCompanyObj.userId = result[0]?.id;

        // 赋值给最外层字段
        moduleItem.ecopartnerName = moduleItem.ownCooperateData.company || "";
        moduleItem.ecologicalCapacityName = result[0]?.realName || "";
        moduleItem.ecologicalCapacityPhone = result[0]?.phone || "";
      } else {
        moduleItem.selectCompanyList = {};
        moduleItem.ipartnerId = undefined;
        moduleItem.ownCooperateData.phone = "";
        moduleItem.selectPhone = "";
        moduleItem.selectCompanyObj.userId = "";
        moduleItem.ecopartnerName = "";
        moduleItem.ecologicalCapacityName = "";
        moduleItem.ecologicalCapacityPhone = "";
      }
    };
    const handleCloseSugget = () => {
      data.showSuggest = false;
      data.formDataDeal.suggest = '';
    };
    const handleOkSugget = () => {
      dealFormRef.value.validate().then(() => {
        data.showSuggest = false;
        if (data.companyAload) {
          // submit();
          // 同意反馈
          agreeApply()
        } else {
          refuseApply();
        }
      }).catch(() => { });
    };
    const agreeApply = () => {
      let index = data.currentModuleIndex;
      if (index === null) return;
      const item = data.formData.moduleForm[index];
      if (!item || !item.taskId) return;

      const postData = [{
        taskId: item.taskId,
        procInsId: item.procInstId,
        comment: data.formDataDeal.suggest,
        variables: {
          chooseType: "1",
          selfUser: item.selectCompanyList ? item.selectCompanyList.isOwned === 1 : false,
          customDataForm: JSON.stringify({ ...item.textList[0].allParseData }),
        },
        chooseType: "1",
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
        operateOrderById(Route.query.orderId).then((res2) => {
          let tempProInstList = res2.data.procInstList;
          if (tempProInstList && tempProInstList.length > 0) {
            // 该工单仍有待办，操作完成
            reModule();
            getData();
          } else {
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
          }
        })
      }).finally(() => {
        data.addLoading = false;
      });
    }
    // 厂商拒绝支撑
    const refuseApply = () => {
      let index = data.currentModuleIndex;
      if (index === null) return;
      const item = data.formData.moduleForm[index];
      if (!item || !item.taskId) return;
      const postData = [{
        taskId: item.taskId,
        procInsId: item.procInstId,
        comment: data.formDataDeal.suggest,
        variables: {
          chooseType: "2",
          selfUser: item.selectCompanyList ? item.selectCompanyList.isOwned === 1 : false,
        },
        chooseType: "2",
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        data.formDataDeal.suggest = "";
        operateOrderById(Route.query.orderId).then((res2) => {
          let tempProInstList = res2.data.procInstList;
          if (tempProInstList && tempProInstList.length > 0) {
            // 该工单仍有待办，操作完成
            reModule();
            getData();
          } else {
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
          }
        })
      });
    };
    //交付经理评分
    const submitBtn = (moduleIndex) => {
      data.operateIndex = moduleIndex;
      data.addLoading = true;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      let tempTextList = operateModule.textList.filter((i) => i.enterpriseId);
      tempTextList = tempTextList.filter((i) => i.dealType && i.dealType == "1");
      const requiredFields = [
        "responseTimelinessRate",
        "securityStandardization",
        "durationGuarantee",
        "deliverySatisfaction",
        "abilityCollaboration"
      ];
      const hasEmpty = tempTextList.some(item =>
        requiredFields.some(field =>
          item[field] === null || item[field] === undefined || item[field] === ''
        )
      );
      if (hasEmpty) {
        message.warning("评分不能为空，请填写所有评分项");
        data.addLoading = false;
        return;
      }
      const addScoreInfo = operateModule.textList.filter((item) => item.enterpriseId && (!item.scored && item.dealType == '1'));
      if (tempTextList.length === 0) return null;
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: "评分完毕",
        variables: {
          addScoreInfo: JSON.stringify({ writeScore: addScoreInfo, }),
        }
      }]
      if (postData.length == 0) {
        message.warning("请至少选择一个生态厂商进行评分");
        data.addLoading = false;
        return;
      }
      batchSubmitWithdrawProcess(postData).then(res => {
        message.success(res.msg);
        operateOrderById(Route.query.orderId).then((res2) => {
          let tempProInstList = res2.data.procInstList;
          if (tempProInstList && tempProInstList.length > 0) {
            // 该工单仍有待办，操作完成
            reModule();
            getData();
          } else {
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
          }
        })
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=waitWork&refresh="
      }).finally(() => {
        data.addLoading = false;
      });
    };
    const disabledDate = (current) => {
      const today = dayjs().startOf("day");
      return current && current < today;
    };
    // 获取新增厂商时的合作方的可选项数据
    const fetchData = async () => {
      fetching.value = true;
      try {
        const response = await selectTree();
        const mockData = response.data.filter((item) => {
          return item.id != "1189";
        });
        data.teamOldList = mockData || [];
        displayOptions.value = mockData;
      } catch (error) {
        console.error("Failed to fetch options:", error);
        displayOptions.value = [];
      } finally {
        fetching.value = false;
      }
    };
    // 获取新增厂商时的自有方的可选项数据
    const fetchOwnData = async () => {
      ownFetching.value = true;
      try {
        const response = await getOwnerUserList();
        const mockData = response.data.map(item => {
          if (item.name === '苏移集成') {
            return {
              ...item,
              name: '江苏移动信息系统集成有限公司'
            };
          }
          return item;
        });
        displayOwnPartnerOptions.value = mockData || [];
      } catch (error) {
        console.error("Failed to fetch own options:", error);
        displayOwnPartnerOptions.value = [];
      } finally {
        ownFetching.value = false;
      }
    };
    const dealContent = (v) => {
      if (!v) {
        return "-";
      }

      // 处理日期对象
      if (v && typeof v === "object" && v.format) {
        return v.format("YYYY年MM月DD日");
      }

      return v;
    };
    onMounted(() => { fetchData(); fetchOwnData(); });
    const setFileData = (fileInfo, moduleItem) => {
      moduleItem.fileList = fileInfo.fileList;
    }
    const viewFileData = (view) => {
      data.viewLoading = view;
    };
    // 添加formatDate函数
    const formatDate = (dateValue) => {
      if (!dateValue) return "-";

      // 如果是dayjs对象
      if (dateValue && typeof dateValue === "object" && dateValue.format) {
        return dateValue.format("YYYY年MM月DD日");
      }

      // 如果是字符串格式的日期
      try {
        return dayjs(dateValue).format("YYYY年MM月DD日");
      } catch (error) {
        console.error("日期格式化错误:", error);
        return dateValue;
      }
    };

    function debounce(fn, delay) {
      let time = null;
      return function () {
        let context = this;
        let args = arguments;
        if (time) {
          clearTimeout(time);
        }
        time = setTimeout(() => {
          // fn.call(this)
          fn.apply(context, args);
        }, 600);
      };
    }
    const dealScore = (v) => {
      if (v) {
        return v;
      } else {
        return "-";
      }
    };
    const dealScoreNew = (v) => {
      if (v == "-1") {
        return "C";
      } else if (v == "0.5") {
        return "B";
      } else if (v == "1") {
        return "A";
      } else {
        return "-";
      }
    };
    const download = (file) => {
      const href = file.url || file.fileUrl;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
      return false;
    };
    const view = (file) => {
      data.viewLoading = true;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      pptTopdf({
        filePath: file.path,
      }).then((res) => {
        if (res.code == 200) {
          data.viewLoading = false;
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1];
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: file.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
      return false;
    };
    const ecoPartnerChange = (val, moduleIndex) => {
      const moduleItem = data.formData.moduleForm[moduleIndex];
      moduleItem.ownCooperateData = {
        company: undefined,
        contact: undefined,
        phone: undefined,
        area: undefined,
      };
      moduleItem.ownContanctList = [];

      moduleItem.CooperateData.contact = null;
      moduleItem.CooperateData.phone = null;
      let list = displayOptions.value.filter((item) => {
        return item.name == val;
      });
      if (list.length > 0) {
        const item = list[0];
        // console.log('生态', item);
        moduleItem.CooperateData.company = item.name;
        moduleItem.CooperateData.sync = item.sync;
        moduleItem.CooperateData.auth = item.auth;
        moduleItem.CooperateData.area = item.address || "江苏省";
        moduleItem.contanctList = item.contactList || [];
        moduleItem.selectCompanyObj.ecopartnerId = item.id;
      } else {
        moduleItem.CooperateData.sync = undefined;
        moduleItem.CooperateData.auth = undefined;
        moduleItem.CooperateData.area = undefined;
        moduleItem.contanctList = [];
      }
    };
    const ecoPartnerSearch = debounce((val) => {
      fetching.value = true;
      selectTree({ name: val }).then((res) => {
        fetching.value = false;
        const mockData = res.data.filter((item) => {
          return item.id != "1189";
        });
        displayOptions.value = mockData;
      });
    }, 600);
    const ownPartnerChange = (val, moduleIndex) => {
      const moduleItem = data.formData.moduleForm[moduleIndex];

      // 选自有能力方，清空生态能力方选择
      moduleItem.CooperateData = {
        company: undefined,
        contact: undefined,
        phone: undefined,
        area: undefined,
        auth: undefined,
        sync: undefined,
      };
      moduleItem.contanctList = [];
      moduleItem.selectId = "";
      moduleItem.selectCompanyList = {};
      moduleItem.ownCooperateData.contact = null;
      moduleItem.ownCooperateData.phone = null;
      moduleItem.ownCooperateData.area = "江苏省";
      moduleItem.ownCooperateData.company = moduleItem.ownCooperateData.company === '苏移集成' ? '江苏移动信息系统集成有限公司' : moduleItem.ownCooperateData.company;
      let list = displayOwnPartnerOptions.value.filter((item) => {
        return item.name == val;
      });
      if (list.length > 0) {
        const item = list[0];
        moduleItem.ownContanctList = item.users || [];
        moduleItem.selectCompanyObj.ecopartnerId = list[0].id;
      } else {
        moduleItem.ownContanctList = [];
        moduleItem.ownCooperateData.area = undefined;
      }
    };
    const handleChange = (val) => {
    };
    const handleCodeTypeChange = (e) => {
    };

    // 删除支撑模块
    const handleDeleteModule = (index) => {
      if (data.formData.moduleForm.length > 1) {
        data.formData.moduleForm.splice(index, 1)
      }
    };
    // 重置formData
    const reModule = () => {
      data.formData.moduleForm = [{
        uid: null,
        textList: [], // 生态厂商列表
        tableData1: [],
        projectContent: "", // 建设内容描述
        projectAmount: undefined, // 项目金额
        contractCodeType: '1',
        contractCode: "",// 合同编号或者订单号
        fileList: [],
        ecopartnerName: "",
        ecologicalCapacityName: "",
        ecologicalCapacityPhone: "",
        editDataCompany: [{
          company: [], // 生态厂商
        }],
        ipartnerId: undefined,
        selectCompanyList: {},
        selectCompanyObj: {},// 合作方或自由方对象
        selectPhone: "",
        companyIdList: [], //生态合作方中auth并sync的的公司id的list
        contanctList: [],
        CooperateData: {
          company: undefined,
          contact: undefined,
          phone: undefined,
          area: undefined,
          auth: undefined,
          sync: undefined,
        },
        ownContanctList: [],// 自由方用户list
        ownCooperateData: {
          company: undefined,
          contact: undefined,
          phone: undefined,
          area: undefined,
        },
        reSelectData: [], //厂商拒绝后调度人页面重新选择厂商数据
        reDispatch: false, // 重新调度
        selectId: "",
        rejectCompanyIdlist: [], //拒绝的厂商id
        action: '',
      }];
    };
    // 新增支撑模块
    const addModule = () => {
      data.formData.moduleForm.push({
        uid: null,
        projectContent: "",
        projectAmount: undefined,
        contractCodeType: '1',
        contractCode: "",
        fileList: [],
        ecopartnerName: "",
        ecologicalCapacityName: "",
        ecologicalCapacityPhone: "",
        editDataCompany: [{
          company: [],
        }],
        selectCompanyObj: {},// 合作方或自由方对象
        selectPhone: "",
        contanctList: [],
        CooperateData: {
          company: undefined,
          contact: undefined,
          phone: undefined,
          area: undefined,
          auth: undefined,
          sync: undefined,
        },
        ownContanctList: [],// 自由方用户list
        ownCooperateData: {
          company: undefined,
          contact: undefined,
          phone: undefined,
          area: undefined,
        },
        action: '',
      });
    };
    // 添加编号验证规则
    const validateContractCode = (rule, value) => {
      if (!value) {
        return Promise.resolve();
      }
      if (!/^[a-zA-Z0-9]+$/.test(value)) {
        return Promise.reject(new Error('只能输入英文或数字'));
      }
      if (value.length > 30) {
        return Promise.reject(new Error('最多只能输入30个字符'));
      }
      return Promise.resolve();
    };

    const contractPlaceholder = (type) => {
      if (type == 1) return '请输入后项合同编号';
      if (type == 2) return '请输入订单编号';
      return '';
    };

    const toCompanyDetail = (item) => {
      let phone = JSON.parse(localStorage.getItem("userInfo")).phone;
      if (item.ecopartnerId > 10000) {
        window.open(
          // "http://***********:8013/static/detail?partner_id=" +
          "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.ecopartnerId + "&token=bhubh3333ugy&phone=" + phone, "_blank"
        );
      } else {
        window.open(
          // "http://***********:8013/static/detail?partner_id=" +
          "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.enterpriseId + "&token=bhubh3333ugy&phone=" + phone, "_blank"
        );
      }
    };
    const addCooperate = (value, index) => {
      data.companyId = value.name;
      data.showAdd = true;
      data.formData.moduleForm[index].companyIdList = value.company.map(item => { return item.enterpriseId });
      data.currentModuleIndex = index;
    };
    const closeAdd = () => {
      data.isSyncAuth = false;
      data.showAdd = false;
      data.contanctList = [];
      data.CooperateData = {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        duty: undefined,
        area: undefined,
        sync: undefined,
        auth: undefined,
        enterpriseId: undefined,
      };
    };
    const ecologyChangeOld = (val) => {
      data.CooperateData.contanct = null;
      data.CooperateData.phone = null;
      let list = data.teamOldList.filter((item) => {
        return item.name == val;
      });

      if (list && list.length > 0) {
        data.CooperateData.sync = list[0].sync;
        data.CooperateData.auth = list[0].auth;
        data.CooperateData.totalScore = list[0].totalScore;
        data.CooperateData.area = list[0].address != null ? list[0].address : "江苏省";
        data.contanctList = list[0].contactList;
        if (data.action == "reSelectPage") {
          data.CooperateData.enterpriseId = list[0].enterpriseId;
        }
        data.comId = list[0].enterpriseId;
      }

      if (data.CooperateData.sync == 1 && data.CooperateData.auth == 1) {
        data.isSyncAuth = false;
      } else {
        data.isSyncAuth = true;
      }
    };
    function debounce(fn, delay) {
      let time = null;
      return function () {
        let context = this;
        let args = arguments;
        if (time) {
          clearTimeout(time);
        }
        time = setTimeout(() => {
          // fn.call(this)
          fn.apply(context, args);
        }, 600);
      };
    }
    const selectUser = (val) => {
      let tempInfo = data.contanctList;
      const result = tempInfo.filter((item) => {
        return item.contactName === val;
      });
      data.CooperateData.phone = result[0].contactPhone;
      data.CooperateData.enterpriseId = result[0].enterpriseId;
      data.CooperateData.area = result[0].contactAddress ? result[0].contactAddress : data.CooperateData.area;
      data.CooperateData.userId = result[0].userId;
      data.CooperateData.approve = result[0].approve;
    };
    const selectUserCom = (value, item) => {
      item.contactName = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).contactName;
      const selectedCompany = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      );
      if (selectedCompany) {
        item.contactPhone = selectedCompany.contactPhone;
      }
      item.userId = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).userId;
    };
    // 新增生态厂商
    const submitAdd = async () => {
      try {
        await addFormRef.value?.validate();
        if (data.formData.moduleForm[data.currentModuleIndex].action == "reSelectPage") {
          const isRejected = data.formData.moduleForm[data.currentModuleIndex].rejectCompanyIdlist?.some(item => item.userId == data.CooperateData.userId)

          if (isRejected) {
            message.warning("该能力方联系人已经拒绝，无法添加");
            return;
          }

          let com = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            userId: data.CooperateData.userId,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
            totalScore: data.CooperateData.totalScore,
          };
          // data.dataCompanyNew[0].company.push(com);
          data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.push(com);
          data.formData.moduleForm[data.currentModuleIndex].companyIdList = data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.map((item) => { return item.enterpriseId; });
          data.CooperateData = {
            company: undefined,
            contanct: undefined,
            phone: undefined,
            area: undefined,
          };

          data.showAdd = false;
        } else if (data.action == "edit") {
          let com = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            userId: data.CooperateData.userId,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
            totalScore: data.CooperateData.totalScore,
          };
          data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.push(com); // 生态厂商list新增
          data.formData.moduleForm[data.currentModuleIndex].companyIdList = data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.map((item) => { return item.enterpriseId; });
          data.CooperateData = {
            company: undefined,
            contanct: undefined,
            phone: undefined,
            area: undefined,
          };
          data.showAdd = false;
        } else {
          data.formData.moduleForm[data.currentModuleIndex].ecopartnerList.forEach((item) => {
            if (item.name === data.companyId) {
              let com = {
                ecopartnerName: data.CooperateData.company,
                contactPhone: data.CooperateData.phone,
                contactName: data.CooperateData.contanct,
                userId: data.CooperateData.userId,
                contactList: data.contanctList,
                enterpriseId: data.CooperateData.enterpriseId,
                sync: data.CooperateData.sync,
                auth: data.CooperateData.auth,
                approve: data.CooperateData.approve,
                totalScore: data.CooperateData.totalScore,
              };
              item.company.push(com);
            }
          });
        }
        message.success("添加成功");
        data.formData.moduleForm[data.currentModuleIndex].companyIdList = data.formData.moduleForm[data.currentModuleIndex].ecopartnerList[0].company.map((item) => { return item.enterpriseId; });
        data.showAdd = false;
        data.CooperateData = {
          company: undefined,
          contanct: undefined,
          phone: undefined,
          area: undefined,
        };
      } catch (error) { }
    };
    const getCalendarContainer = () => {
      return triggerNode => triggerNode.parentNode;
    }
    return {
      ...toRefs(data),
      zhCN,
      displayOptions,
      displayOwnPartnerOptions,
      fetching,
      ownFetching,
      fileFormRef,
      addFormRef,
      Router,
      Route,
      handleChange,
      ownPartnerChange,
      ecoPartnerChange,
      ecoPartnerSearch,
      setFileData,
      view,
      download,
      viewFileData,
      dealScoreNew,
      dealScore,
      debounce,
      dealContent,
      disabledDate,
      submitBtn,
      handleCloseSugget,
      handleOkSugget,
      cancel,
      selectEcoUser,
      selectOwnUser,
      reSubmit,
      createOrder,
      limitLength,
      handleInputChange,
      formatDate,
      handleDeleteModule,
      reModule,
      addModule,
      handleCodeTypeChange,
      validateContractCode,
      contractPlaceholder,
      submitCompany,
      refuseApplyCompany,
      handleMidSaleFeedback,
      isEcologicalCompany,
      toCompanyDetail,
      addCooperate,
      closeAdd,
      ecologyChangeOld,
      selectUserCom,
      selectUser,
      submitAdd,
      onCheckChange,
      dealFormRef,
      toChinese,
      getCalendarContainer,
    };
  },
});
</script>
<style lang="scss" scoped>
@import "../starWork/css/button.scss";
.form-title-class {
  font-weight: 500;
  font-size: 16px;
  color: #000000;
  line-height: 22px;
  padding-bottom: 18.5px;
  padding-top: 18.5px;
  padding-left: 24px;
  border-bottom: 1px solid rgba(0, 6, 14, 0.08);
}

.module_title {
  display: flex;
  margin: 10px 32px;
  justify-content: space-between;
  align-items: center;
}

.module_group {
  padding: 23px;
  margin: 0 24px 0 32px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #D1D0D8;
}

.module_group:not(:first-child) {
  margin-top: 20px;
}


.box {
  :deep(.ant-select-selector) {
    width: 140px !important;
    // margin-right: 24px;
  }
}

.abInfo {
  margin: 10px 24px;
  padding: 24px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #D1D0D8;

  .top {
    p {
      display: inline-block;
    }
  }
}


:deep(.ant-select-arrow) {
  display: none;
}

.ant-input:focus {
  box-shadow: none !important;
}

:deep(textarea:focus) {
  box-shadow: none;
}

:deep(.ant-form-item-label) {
  width: 110px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.ant-upload) {
  border-radius: 4px;
}

:deep(.ant-upload-list) {
  width: 25%;
}

:deep(.ant-input) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

:deep(textarea) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

.file-list {
  width: 40%;
  cursor: pointer;

  p {
    position: relative;
    padding: 2px 24px 2px 24px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 8px;
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px;
  }

  .iconSize {
    width: 15px;
    height: 15px;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 7px;
  }

  .icon-annex {
    width: 13px;
    height: 13px;
    color: #0c70eb;
    position: absolute;
    top: 2px;
    left: 8px;
  }
}

#addAbilit {
  background: #fff;
  overflow: hidden;
  box-shadow: 4px 4px 8px 0px #f4f5f5;
  border-radius: 8px 8px 8px 8px;
  overflow-y: auto;
  height: calc(100vh - 100px);

  .line {
    width: 100%;
    height: 1px;
    background-image: linear-gradient(to right,
        rgba(0, 6, 14, 0.08) 0%,
        rgba(0, 6, 14, 0.08) 50%,
        transparent 50%);
    background-size: 10px 1px;
    background-repeat: repeat-x;
  }

  .m-img {
    max-width: 78px;
    max-height: 78px;
  }

  .icon {
    display: inline-block;
    width: 4px;
    height: 13px;
    background: #0c70eb;
    box-shadow: 2px 1px 6px 0px rgba(12, 112, 235, 1),
      inset 0px 0px 3px 0px rgba(255, 255, 255, 0.8);
    border-radius: 2px 2px 2px 2px;
    margin-right: 8px;
  }

  .font_F51D0F {
    cursor: pointer;
  }
  .required :deep(.ant-form-item-label::before) {
    content: "*";
    color: red;
  }
}

.provinceBtn {
  display: flex;

  p {
    display: inline-block;
    margin-right: 10px;
  }
}

.el-dialog__header.show-close {
  text-align: center;
}

.el-dialog__body {
  text-align: center;

  .ant-form {
    .ant-row:last-child {
      padding-left: 0px;
    }
  }
}

.ant-checkbox-wrapper {
  display: block;
  text-align: left;
}

.ant-radio-group {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ant-checkbox {
  display: inline-block;
}

.ant-checkbox-wrapper span:last-child {
  display: inline-block;
}

.company_left {
  margin-right: 16px;
}

.ant-checkbox-wrapper+.ant-checkbox-wrapper {
  margin-left: 0;
}

.custom-tooltip {
  color: #fff !important;
  max-width: 600px;
  /* 限制最大宽度 */
  background: rgba(96, 98, 102, 0.9) !important;
}

.el-tooltip__arrow::before {
  background: rgba(96, 98, 102, 0.9) !important;
  border-color: rgba(96, 98, 102, 0.9) !important;
}

.content_control {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  /* 显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-with-help {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.scrollable-table {
  overflow-x: auto;
  /* 启用横向滚动 */
  width: 100%;
  /* 确保宽度占满父容器 */
}

/* 新增评分相关样式 */
.score-control {
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-value {
  display: inline-block;
  width: 30px;
  text-align: center;
  margin: 0 8px;
}

.minus-btn,
.plus-btn {
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

.partner-info {
  text-align: left;
  padding: 0 5px;

  >div {
    margin: 5px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.module-info {
  text-align: left;
  padding: 0 10px;
  word-break: break-word;
  line-height: 1.5;
}

/* 问号图标样式 */
.header-help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  cursor: pointer;
  color: #909399;
}

.header-with-help {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.ant-form {
  gap: 10px;
}

.ant-form-inline .ant-form-item {
  margin-right: 0;
}


.ant-btn-primary {
  background: #0C70EB;
  border-radius: 4px;
  color: #fff !important;
  border: none;
}

.ant-btn-primary:hover {
  background: #509fff !important
}

.ant-btn-primary:focus {
  background: #0C70EB !important
}

.company_left {
  margin-right: 8px;
  font-weight: bold;
}

.company_underline {
  color: #1890ff;
  cursor: pointer;

  &:hover {
    color: #40a9ff;
  }
}

.company_right {
  display: flex;
  justify-content: center;
  // margin-right: 8px;
  gap: 8px;
  min-width: 156px;
  max-width: 176px;
  height: 28px;
  font-size: 15px;
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(77, 120, 170, 0.1);
  border-radius: 20px 20px 20px 20px;
}

.box {
  :deep(.ant-select-selector) {
    width: 140px !important;
    // margin-right: 24px;
  }
}

:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  border: 1px solid #d9d9d9 !important;
}
</style>
<style lang="scss">
.resize-table-header-line.el-table {

  // 默认表头和单元格使用默认光标
  th.el-table__cell,
  td>.cell {
    cursor: default !important;
  }

  // 只在表头分隔线位置显示调整列宽光标
  th.el-table__cell {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px; // 分隔线热区宽度
      height: 100%;
      cursor: default;
      transform: translateX(50%); // 居中显示
    }

    &:hover::after {
      cursor: col-resize !important;
    }
  }
}

.add_btn {
  position: absolute;
  right: 8px;
}

.table-row-wrapper {
  overflow-x: auto;
  max-height: 290px;
}
</style>