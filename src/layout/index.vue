<template>
  <a-layout class="layout">
    <a-layout-header
      style="background: #ffffff; opacity: 0.8; height: 60px; line-height: 60px"
    >
      <a-row class="layout_top">
        <a-col :span="3" class="flex align-center">
          <img
            class="logo"
            :class="{ pointer: !isEcoSystemUser }"
            src="@/assets/images/newProject/logoA.svg"
            @click="!isEcoSystemUser && backHome()"
          />
        </a-col>
        <a-col :span="16" class="flex align-start" style="z-index: 9999;">
          <div id="tabContainer">
            <div class="flex align-center">
              <template v-for="(item, index) in navigateList" :key="index">
                <a-dropdown overlayClassName="navText">
                  <div
                    :class="[
                      'headTitle',
                      {
                        tabActive: item.name == layoutName,
                      },
                    ]"
                    @mouseover="hoverChange('enter', item.name)"
                    @mouseout="hoverChange('leave', item.name)"
                    @click="menuNavigate(item)"
                  >
                    <span>{{ item.name }}</span>
                  </div>
                  <template #overlay v-if="item.children">
                    <a-menu>
                      <template v-for="menu in item.children" :key="menu.id">
                        <a-menu-item
                          v-if="menu.visible === 1"
                          @click="menuNavigate(menu)"
                        >
                          <span>{{ menu.name }}</span>
                        </a-menu-item>
                      </template>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>
            </div>
          </div>
        </a-col>
        <a-col :span="5">
          <div class="flex align-center rightContent">
            <div class="headAvatar" v-if="combinedTasklist.length > 0">
              <a-dropdown overlayClassName="menuText">
                <div
                  v-if="!isEcoSystemUser"
                  class="flex align-center rightContent"
                  style="position: relative"
                >
                  <img src="@/assets/images/home/<USER>" class="sets" />
                  <div
                    class="margin_r_16"
                    style="color: #24456a; margin: 0 24px 0 6px"
                    @click="backHome"
                  >
                    <a style="color: #24456a">工单</a>
                  </div>
                  <div
                    style="
                      width: 1px;
                      height: 20px;
                      background-color: #dbe2edff;
                    "
                  ></div>
                  <div
                    style="
                      position: absolute;
                      width: 16px;
                      height: 16px;
                      border-radius: 8px;
                      background-color: #f5222dff;
                      color: #ffffff;
                      top: 14px;
                      left: 10px;
                      text-align: center;
                      line-height: 16px;
                      font-size: 10px;
                    "
                  >
                    {{ processAllNum }}
                  </div>
                </div>
                <template #overlay>
                  <a-menu style="width: 500px">
                    <div style="max-height: 500px; overflow: auto">
                      <a-menu-item
                        v-for="(item, index) in combinedTasklist"
                        :key="index"
                        @click="toBackendManage(item)"
                      >
                        <div class="workOrder">
                          <div class="workOrder_image"></div>
                          <div class="workOrder_body">
                            <p class="workOrder_body_title">
                              {{ getTitle(item) }}
                            </p>
                            <p class="workOrder_body_time">
                              {{ item.createTime }}
                            </p>
                          </div>
                          <div class="workOrder_type">
                            {{ item.typeName || typeChange(item.type) }}
                          </div>
                        </div>
                      </a-menu-item>
                    </div>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>

            <!--<img src="@/assets/images/home/<USER>" class="sets" />
            <div class="margin_r_16" style="color: #24456a; margin: 0 24px 0 6px" @click="backHome">
              <a style="color: #24456a">工作台</a>
            </div>-->
            <!-- <a-badge dot v-if="readShow">
              <img src="@/assets/images/home/<USER>" class="sets pointer" @click="noticeModal" />
            </a-badge>
            <img src="@/assets/images/home/<USER>" class="sets pointer" @click="noticeModal" v-else />
            <div class="margin_r_16" style="color: #24456a; margin: 0 24px 0 6px" @click="noticeModal">
              <a style="color: #24456a">版本公告</a>
            </div> -->
            <!-- <div
              class="flex align-center rightContent"
              style="position: relative"
            >
              <span style="color: #24456a; cursor: pointer" @click="toCommunity"
                >麒麟社区</span
              >
              <div
                v-if="shequNum != undefined"
                style="
                  position: absolute;
                  width: 16px;
                  height: 16px;
                  border-radius: 8px;
                  background-color: #f5222dff;
                  color: #ffffff;
                  top: 14px;
                  left: 53px;
                  text-align: center;
                  line-height: 16px;
                  font-size: 10px;
                "
              >
                {{ shequNum }}
              </div>
            </div> -->
            <div
              class="margin_r_16"
              style="color: #24456a; margin: 0 24px 0 6px"
            >
              <span style="color: #24456a; cursor: pointer" @click="showModal"
                >建议反馈</span
              >
            </div>
            <div class="headAvatar">
              <div class="headName flex align-center just-center" @click="handleHeadNameClick()">
                <img v-if="loginSex == '女'" src="@/assets/images/woman.png" alt="" />
                <img v-else src="@/assets/images/man.png" alt="" />
              </div>
              <a-dropdown overlayClassName="menuText">
                <a class="ant-dropdown-link">
                  {{ loginname }}
                  <DownOutlined />
                </a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="toManage">
                      <span>管理后台</span>
                    </a-menu-item>
                    <a-menu-item @click="personInfo">
                      <span>个人信息</span>
                    </a-menu-item>
                    <a-menu-item @click="noticeModal">
                      <a-badge dot v-if="readShow">
                        <span style="color: rgba(0, 6, 14, 0.6)">版本公告</span>
                      </a-badge>
                      <span v-else>版本公告</span>
                    </a-menu-item>
                    <a-menu-item @click="toView()">
                      <span>帮助中心</span>
                    </a-menu-item>
                    <!--<a-menu-item v-if="haveGuide" @click="toView(0)">
                      <span>平台指南</span>
                    </a-menu-item>
                    <a-menu-item v-if="haveBook" @click="toView(1)">
                      <span>用户手册</span>
                    </a-menu-item>-->
                    <a-menu-item @click="revampPsd">
                      <span>修改密码</span>
                    </a-menu-item>
                    <a-menu-item @click="logOut">
                      <span>退出登录</span>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-layout-header>

    <a-layout-content
      id="layout_content"
      :class="[showChatHistory ? 'flex' : '']"
    >
      <div v-if="showChatHistory" class="chatHistory">
        <chat-history />
      </div>
      <router-view style="flex: 1" v-slot="{ Component, route }">
        {{}}
        <keep-alive
          :include="[
            'AI',
            'customizedList',
            'visionPolicy',
            'newAllProject',
            'newProject',
            'module',
            'moduleList',
            'solveNew',
            'topContent',
            'prefecture',
          ]"
        >
          <component :is="Component" />
        </keep-alive>
      </router-view>

      <!-- <router-view /> -->
    </a-layout-content>
  </a-layout>

  <div v-if="overviewShow">
    <div ref="target2" class="draggable-box2" :style="{ left: `${x2}px`, top: `${y2}px` }" v-if="showShoppingCar && shopCard">
      <ShoppingCart class="shopping" :showShequ="showShequ" :borderShow="borderShow" @shopEnter="shopEnter" @shopLeave="shopLeave" :type="dataType"></ShoppingCart>
    </div>
    <div ref="target1" class="draggable-box1" :style="{ left: `${x1}px`, top: `${y1}px` }" v-if="!showShequ && shequCard">
      <div class="comBtn">
        <div :class="['comBtnShequ',{ shequActive: cardInfo.shequShow }, { comBtnShequActive: borderShow && !showShoppingCar }, { comActive: borderShow && showShoppingCar}]"
          @mouseenter="cardEnter('shequ')" @mouseleave="cardLeave('shequ')" @click="toCommunity">
          <img class="shequ" src="@/assets/shequ.png" />
          <p class="prepare">麒麟<br/>社区</p>
        </div>
        <div class="comNum" v-if="shequNum != undefined">{{ shequNum }}</div>
      </div>
    </div>
    <div ref="target3" :class="['draggable-box3',{ zIndexBigClass: zIndexBig }]" v-if="!showShequ" :style="{ left: `${x3}px`, top: `${y3}px` }">
      <div :class="['suspendCard',{ suspendActive: cardInfo.cardShow },{ cardActive: showShoppingCar }]"
        @mouseenter="cardEnter('suspend')" @mouseleave="cardLeave('suspend')" @click="closeCard">
        <div v-if="shopCard || shequCard" class="normal">></div>
        <div v-else class="normal"><</div>
      </div>
    </div>
  </div>

  <a-modal
    :visible="previewVisible"
    title="建议反馈"
    @cancel="closeModal"
    :footer="null"
    width="800px"
  >
    <suggest-form @submit-cancel="closeModal"></suggest-form>
  </a-modal>

  <a-modal
    :visible="versionVisible"
    :closable="false"
    :footer="null"
    width="800px"
    class="versionModal"
  >
    <version-notice
      @know-cancel="knowClose"
      @read-notice="getReadNotice"
      v-if="versionVisible"
    />
  </a-modal>

  <resetPassword
    v-if="resetPass.isShow"
    :resetPass="resetPass"
    :form="resetData"
  ></resetPassword>

  <div class="overlayBac" v-if="showPass">
    <div class="tip">
      <div class="banner"></div>
      <div class="AItitle">
        <img
          src="@/assets/images/newProject/robot.png"
          alt=""
          style="width: 40px; height: 50px"
        />
        <div class="title">
          您使用的仍是系统默认初始密码或已90天未登录，请修改密码后登录！
        </div>
      </div>
      <div>
        <a-form
          ref="formRef"
          :model="formData"
          labelAlign="right"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row class="passWardClass">
            <a-col :span="13">
              <a-form-item
                label="新密码"
                name="newPassword"
                :colon="false"
                :wrapper-col="{ offset: 1, span: 16 }"
              >
                <a-input
                  v-model:value="formData.newPassword"
                  placeholder="请输入新密码"
                  :type="newInput ? 'password' : 'text'"
                  allowClear
                >
                  <template #suffix>
                    <i
                      :class="[
                        'iconfont',
                        'pointer',
                        newInput ? 'icon-hide' : 'icon-display',
                      ]"
                      style="color: #0c70eb"
                      @click.stop="typeInput('new')"
                    ></i>
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="13">
              <a-form-item
                label="确认新密码"
                name="okPass"
                :colon="false"
                :wrapper-col="{ offset: 1, span: 16 }"
              >
                <a-input
                  v-model:value="formData.okPass"
                  placeholder="请确认新密码"
                  :type="okInput ? 'password' : 'text'"
                  allowClear
                >
                  <template #suffix>
                    <i
                      :class="[
                        'iconfont',
                        'pointer',
                        okInput ? 'icon-hide' : 'icon-display',
                      ]"
                      style="color: #0c70eb"
                      @click.stop="typeInput('ok')"
                    ></i>
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="flex just-center botBtn">
          <a-button type="primary" @click="submit">确定</a-button>
        </div>
      </div>
    </div>
  </div>
  <a-modal
    class="chooseProductModal"
    v-model:visible="showChooseProduct"
    :mask-closable="true"
    :closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="700px"
  >
    <div class="productBanner margin_b_32"></div>
    <div class="productContent flex just-center">
      <div class="toProductBag margin_r_40">
        <div class="flex just-center" @click="goCustomized()">
          <div class="productImg pointer"></div>
        </div>
        <p class="title text-center">商客市场</p>
        <p class="words text-center">通过商客市场定制产品包</p>
      </div>
      <div class="toMore">
        <div class="productImg flex just-center">
          <img src="../assets/images/layout/Group 10919.png" alt="" />
        </div>
        <p class="title text-center">其他市场</p>
        <p class="words text-center">其他市场正在开发中...</p>
      </div>
    </div>
    <!-- <div class="productBox">
      商客市场
    </div> -->
  </a-modal>
</template>

<script>
import { defineComponent, reactive, toRefs, computed, watch, onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import suggestForm from "./components/suggestForm.vue";
import versionNotice from "./components/versionNotice.vue";
import resetPassword from "@/views/login/resetPassword.vue";
import ShoppingCart from "@/components/ShoppingCart/index.vue";
import { getAbouteMe } from "@/api/community/index.js";
import {
  LeftOutlined,
  RightOutlined,
  DownOutlined,
} from "@ant-design/icons-vue";
import { currentTab } from "@/store";
import { getGuideList, getGuideRoleList } from "../api/portal/home";
import { getLogOut, getCombinedTasklist } from "../api/login/login.js";
import { useHomeStore } from "@/store";
import { backendUrl } from "@/utils/getUrl";
import { v4 as uuidv4 } from "uuid"; // 使用uuid库生成随机字符串
import eventBus from "@/utils/eventBus";
import { message } from "ant-design-vue";
import { resetPwds } from "@/api/info";
import AES from "@/utils/cryptoPwd.js";
import { readNotice } from "@/api/versionNotice/index.js";
import { getHelp } from "@/api/system/user.js";
import { babelParse } from "vue/compiler-sfc";
import { el, fa } from "element-plus/es/locale/index.mjs";
import chatHistory from "@/components/chatHistory/index.vue";
import { useDraggable } from '@vueuse/core'
export default defineComponent({
  components: {
    DownOutlined,
    LeftOutlined,
    RightOutlined,
    suggestForm,
    ShoppingCart,
    versionNotice,
    chatHistory,
    resetPassword
  },
  setup() {
    const getCurTab = currentTab();
    const Router = useRouter();
    const Route = useRoute();
    const baseURL = backendUrl();
    const counterStore = useHomeStore();

    //社区
    const target1 = ref(null)
    const getInitialPosition1 = () => {
	    const right = 0
	    const bottomPercent = 9
	    const bottomPx = 90
	    const computedBottom = (window.innerHeight * bottomPercent / 100) + bottomPx
	    return {
	      x: window.innerWidth - 60 - right,
	      y: window.innerHeight - 90 - computedBottom
	    }
	  }
    const zIndexBig = ref(false)
		//购物车
		const target2 = ref(null)
    const getInitialPosition2 = () => {
	    const right = 0
	    const bottomPercent = 9
	    const bottomPx = 0
	    const computedBottom = (window.innerHeight * bottomPercent / 100) + bottomPx
	    return {
	      x: window.innerWidth - 60 - right,
	      y: window.innerHeight - 90 - computedBottom
	    }
	  }
    const target3= ref(null)
    const getInitialPosition3 = () => {
	    const right = 17
	    const bottomPercent = 9
	    const bottomPx = 90
	    const computedBottom = (window.innerHeight * bottomPercent / 100) + bottomPx
	    return {
	      x: window.innerWidth - 60 - right,
	      y: window.innerHeight - 90 - computedBottom
	    }
	  }

		const { x: x1, y: y1 } = useDraggable(target1, {
		  initialValue: getInitialPosition1(),
		  preventDefault: true,
		  stopPropagation: true,
		  disabled: true
		})
		const { x: x2, y: y2 } = useDraggable(target2, {
		  initialValue: getInitialPosition2(),
		  preventDefault: true,
		  stopPropagation: true,
		  disabled: true
		})

    const { x: x3, y: y3 } = useDraggable(target3, {
		  initialValue: getInitialPosition3(),
		  preventDefault: true,
		  stopPropagation: true,
		  disabled: true
		})

		const updatePosition = () => {
	    const pos1 = getInitialPosition1()
	    x1.value = pos1.x
	    y1.value = pos1.y
	    const pos2 = getInitialPosition2()
	    x2.value = pos2.x
	    y2.value = pos2.y
      if (isCardClosed.value) {
        const dockX = window.innerWidth - 30
        const pos3 = getInitialPosition3()
        x3.value = dockX
        y3.value = pos3.y
      } else {
        const pos3 = getInitialPosition3()
        x3.value = pos3.x
        y3.value = pos3.y
      }
	 }

		// 添加响应式更新
	  onMounted(() => {
	    updatePosition();
	    window.addEventListener('resize', updatePosition)
	  })

    //重置密码
    const formRef = ref();
    const validatePass = async (rule, value) => {
      let reg = /^(?=.*[A-Za-z\W])(?=.*\d)[A-Za-z\d\W]{6,16}$/;
      if (!value) return Promise.reject("请输入新密码");

      if (!reg.test(value))
        return Promise.reject(
          "密码必须是6-16位数字、字母或字符组合（不能是纯数字）"
        );

      return Promise.resolve();
    };
    const validateNewPass = async (rule, value) => {
      if (!value) return Promise.reject("请输入新密码");

      if (value != data.formData.newPassword)
        return Promise.reject("两次密码不一致");

      return Promise.resolve();
    };

    const typeChange = (value) => {
      let typeStr = {
        0: "下架审核",
        1: "上架审核",
        2: "能力撰写",
        3: "修改审核",
        4: "下载申请",
        5: "生态关联",
        6: "企业资料",
        7: "商机工单",
        8: "售前调度",
        11: "生态更新",
        9: "售中调度",
      };
      return typeStr[value];
    };

    // 获取工单标题方法
    const getTitle = computed(() => {
      return (value) => {
        if (value.formType == 0) {
          return value.title;
        } else {
          let json = value.procVars;
          return json.title;
        }
      };
    });

    const data = reactive({
      isEcoSystemUser: false, // 是否生态方，是则不可查看工单和跳转到ai首页
      combinedTasklist: "",
      processAllNum: "",
      shequNum: counterStore.shequNum,
      newPassword1: "",
      newPassword2: "",
      showPass: false,
      showShequ: false,
      shopCard: true,
      shequCard: true,
      versionVisible: false,
      current: null,
      layoutName: getCurTab.layoutActive,
      loginName: "",
      headName: "",
      navigateList: [],
      previewVisible: false,
      readShow: false,
      showShop: counterStore.contralShop,
      dataType: "预选组合",
      showShoppingCar: true,
      formData: {},
      rules: {
        //oldPassword: { required: true, message: "请输入旧密码" },
        newPassword: { required: true, validator: validatePass },
        okPass: { required: true, validator: validateNewPass },
      },
      newInput: true,
      oldInput: true,
      okInput: true,
      overviewShow: true,
      showChooseProduct: false,
      productBagLinkUrl: "",
      haveGuide: false,
      haveBook: false,
      showChatHistory: false,
      headNameClickCount: 0,
      borderShow: false,
      resetPass: {
        fromType: 2,
        procedure: 1,
        isShow: false,
      },
      resetData: {},
      cardInfo: {}
    });
    const getQueryParam = (url) => {
      const match = url.match(/=(.*)/);
      data.layoutName = match ? match[1] : getCurTab.layoutActive;
    };
    getQueryParam(decodeURI(window.location.href));
    eventBus.on("urlRefresh", getQueryParam(decodeURI(window.location.href)));
    watch(
      () => getCurTab.layoutActive,
      (val) => {
        data.layoutName = val;
      }
    );
    watch(
      () => counterStore.contralShop,
      (nval) => {
        data.showShop = nval;
        if (data.showShop == true) {
          data.dataType = "预选组合";
        } else {
          data.dataType = "订购产品";
        }
      },
      { deep: true }
    );
    watch(
      () => counterStore.shequNum,
      (nval) => {
        data.shequNum = nval;
      },
      { deep: true }
    );
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    data.overviewShow = userInfo.roleKeyList.length === 1 && userInfo.roleKeyList.includes("ecologicalPartner") ? false: true,
    data.headName = userInfo.realName.slice(0, 1).toUpperCase();
    data.isEcoSystemUser = userInfo.roleKeyList.includes('ecologicalPartner');
    const hoverChange = (action, name) => {
      if (action === "enter") {
        data.layoutName = name;
      } else {
        data.layoutName = getCurTab.layoutActive;
      }
    };
    const menuNavigate = (val) => {
      getCurTab.layoutActive = val.name;
      let origin = window.location.origin;
      let urlsecond = val.linkUrl.split("#")[1];
      if (val.name == "产品包定制") {
        data.productBagLinkUrl = origin + val.linkUrl;
        data.showChooseProduct = true;
      } else {
        if (urlsecond.indexOf("newProject") > -1) {
          window.location.replace(
            // origin + val.linkUrl //发布
            "http://localhost:5173/#" + urlsecond //本地
          );
        } else if (urlsecond.indexOf("cityAreaNew") > -1) {
          const isSysAdmin = userInfo.roleKeyList.includes("sysAdmin");
          const isSuperVip = userInfo.roleKeyList.includes("superVip");
          if ((isSysAdmin || isSuperVip) && val.name == "地市专区") {
            if (val.children && val.children.length > 0) {
              window.location.replace(
                // origin + val.linkUrl + "?active=" + val.children[0].name //发布
                "http://localhost:5173/#" + urlsecond + "?active=" + getCurTab.layoutActive //本地
              );
            }
          } else {
            window.location.replace(
              // origin + val.linkUrl + "?active=" + val.name //发布
              "http://localhost:5173/#" + urlsecond + "?active=" + getCurTab.layoutActive //本地
            );
          }
        } else {
          window.location.replace(
            // origin + val.linkUrl + "?active=" + getCurTab.layoutActive //发布
            "http://localhost:5173/#" + urlsecond + "?active=" + getCurTab.layoutActive //本地
          );
        }
      }
    };
    const goCustomized = () => {
      // console.log("data.productBagLinkUrl", data.productBagLinkUrl);
      let urlsecond = data.productBagLinkUrl.split("#")[1];
      if (urlsecond.indexOf("newProject") > -1) {
        window.location.replace(
          data.productBagLinkUrl //发布
          // "http://localhost:5174/#" + urlsecond //本地
        );
      } else {
        window.location.replace(
          data.productBagLinkUrl + "?active=" + getCurTab.layoutActive //发布
          // "http://localhost:5175/#" + urlsecond + "?active=" + getCurTab.layoutActive //本地
        );
      }
      data.showChooseProduct = false;
    };
    const getNavigate = () => {
      //getGuideList({ enable: 1, visible: 1 }).then((res) => {
      getGuideRoleList().then((res) => {
        if (res.code === 200) {
          data.navigateList = res.data;
          data.navigateList.push({
            id: uuidv4(),
            name: "调度中心",
            linkUrl:  "http://localhost:5173/#/dispatchCenter/workbench",
          }); // test调度中心(上线前删除)
          getCurTab.layoutActive = Route.query.active;
          let urlList = [];
          for (let i of res.data) {
            urlList = [...urlList, i];
            if (i.children) {
              for (let j of i.children) {
                urlList = [...urlList, j];
              }
            }
          }
          // console.log('urlList',urlList);
          data.navigateList.forEach((item) => {
            if (item.name == "地市专区") {
              // 系统管理员和超级VIP可以看到专区菜单
              const isSysAdmin = userInfo.roleKeyList.includes("sysAdmin");
              const isSuperVip = userInfo.roleKeyList.includes("superVip");

              // 检查用户是否属于十三个地市（orgId 2-14）
              const isCityUser = userInfo.orgId >= 2 && userInfo.orgId <= 14;

              // 如果既不是系统管理员、超级VIP也不是地市用户，则移除该菜单
              if (!isSysAdmin && !isSuperVip && !isCityUser) {
                const index = data.navigateList.indexOf(item);
                if (index > -1) {
                  data.navigateList.splice(index, 1);
                }
                return;
              }

              // 如果是地市用户，检查是否有对应的专区
              if (
                isCityUser &&
                userInfo.orgName &&
                userInfo.orgName.indexOf("市") !== -1
              ) {
                const cityName = userInfo.orgName.split("市")[0];
                // 检查children中是否有对应的专区
                const hasCityArea =
                  item.children &&
                  item.children.some(
                    (child) => child.name === `${cityName}专区`
                  );
                if (!hasCityArea) {
                  // 如果没有对应的专区，则移除该菜单
                  const index = data.navigateList.indexOf(item);
                  if (index > -1) {
                    data.navigateList.splice(index, 1);
                  }
                  return;
                }
                // 如果有对应的专区，修改菜单名称为"XX专区"并移除children
                item.name = `${cityName}专区`;
                item.children = null;
                // 将对应的专区链接赋值给父级菜单
                if (item.children) {
                  const cityArea = item.children.find(
                    (child) => child.name === `${cityName}专区`
                  );
                  if (cityArea) {
                    item.linkUrl = cityArea.linkUrl;
                  }
                }
              }

              // 如果是系统管理员或超级VIP，设置链接为第一个子菜单的链接
              if (isSysAdmin || isSuperVip) {
                if (item.children && item.children.length > 0) {
                  item.linkUrl = item.children[0].linkUrl;
                }
              }
            }
          });
          // console.log('data.navigateList',data.navigateList);
          localStorage.setItem("urlList", JSON.stringify(urlList));
          localStorage.setItem("homeLogo", res.data[0].cover);
        }
      });
    };
    getNavigate();

    //获取用户待办信息
    const getProcess = () => {
    	if (userInfo.roleKeyList.includes("sysAdmin")) return false
      getCombinedTasklist({ pageNo: 1, pageSize: 100 }).then((res) => {
        if (res.code == 200) {
          data.combinedTasklist = res.data.rows;
          data.processAllNum = res.data.totalRows;
        }
      });
      let param = {
        pageSize: 10,
        pageNo: 1,
      };
      getAbouteMe(param).then((res) => {
        if (res.data.pendingCount) {
          data.shequNum = res.data.pendingCount;
        } else if (res.data.allocatedCount) {
          data.shequNum = res.data.allocatedCount;
        } else {
          data.shequNum = undefined;
        }
      });
    };
    getProcess();

    const getRead = () => {
      readNotice({ userId: userInfo.id }).then((res) => {
        if (res.code == 200) {
          data.readShow = res.data;
        }
      });
    };
    getRead();

    const getReadNotice = () => {
      getRead();
    };

    const leaveChange = () => {
      data.current = null;
    };

    const cardEnter = (type) => {
      if(type == 'suspend') data.cardInfo.cardShow = true;
      if(type == 'shequ') {
        data.cardInfo.cardShow = true;
        data.cardInfo.shequShow = true;
      }
    }

    const cardLeave = (type) =>{
      if(type == 'suspend') data.cardInfo.cardShow = false;
      if(type == 'shequ') {
        data.cardInfo.cardShow = false;
        data.cardInfo.shequShow = false;
      }
    }

    const shopEnter = () =>{
      data.cardInfo.cardShow = true;
    }
    const shopLeave = () =>{
      data.cardInfo.cardShow = false;
    }

    const toManage = (val) => {
      window.location.replace(baseURL);
      // window.open("http://localhost:5174/backend/#/");
    };
    function logOut() {
      getLogOut().then((res) => {
        // 清缓存
        window.localStorage.setItem("token", "");
        window.localStorage.setItem("userInfo", "");
        counterStore.username = "0";
        counterStore.aiBuyListStroe = {};
        counterStore.parentId = "";
        counterStore.productList = [];
        counterStore.showTop = false;
        Router.replace({
          path: "/login",
        });
      });
    };

    const noticeModal = () => {
      data.versionVisible = true;
    };
    eventBus.on('noticeModal',noticeModal)

    const showModal = () => {
      data.previewVisible = true;
    };
    eventBus.on('showModal',showModal)

    const closeModal = () => {
      data.previewVisible = false;
    };

    const knowClose = () => {
      data.versionVisible = false;
      userInfo.versionPopupShowed = 0
      localStorage.setItem('userInfo',JSON.stringify(userInfo))
    };

    const backHome = () => {
      getCurTab.layoutActive = "";
      Router.push({
        name: "home",
      });
    };
    const loginname = computed(() => {
      return userInfo.realName;
    });

    const loginSex = computed(() => {
      return userInfo.sex;
    });

    const abilityHome = () => {
      let pageView = Router.resolve({
        name: "abilityHome",
      });
      window.open(pageView.href, "_blank");
    };
    // 工单通知点击事件
    const toBackendManage = (item) => {
      localStorage.setItem("processInfo", JSON.stringify(item));
      if (item.businessType == 11) {
        // 调度工单跳转到调度中心
        window.location.replace(
          window.location.origin + "/#/dispatchCenter/transfer?to=process&active=调度中心"
        );
      } else {
        // 非调度工单跳转到工单管理
        window.location.replace(
          window.location.origin + "/backend/#/transferNew?to=process"
        );
      }
    };

    const personInfo = () => {
      Router.push({
        name: "personal",
      });
    };
    const revampPsd = () => {
      Router.push({
        name: "revampPwd",
      });
    };
    watch(
      () => Route.name,
      (newVal, oldVal) => {
        if (Route.path.indexOf("dispatchCenter") > -1) {
          data.layoutName = "调度中心";
        }
        if (newVal == "scenarioPlan") {
          data.layoutName = "商客市场";
        }
        if (newVal == "productList") {
          data.layoutName = "标准产品";
        }
        if (newVal == "topContentNew") {
          data.layoutName = "解决方案";
        }
        if (newVal == "moduleList") {
          data.layoutName = "解决方案";
        }
        if (newVal == "AISearch" || newVal == "home") {
          data.showChatHistory = true;
        } else {
          data.showChatHistory = false;
        }
        if (
          newVal == "home" ||
          newVal == "topContentCase" ||
          newVal == "caseDetailNew" ||
          newVal == "topContentProduct" ||
          newVal == "caseProductDetail" ||
          newVal == "viewPageMd" ||
          newVal == "cityAreaDetailNew" ||
          newVal == "cityAreaApplyNew" ||
          newVal == "cityAreaNew" ||
          newVal == "thirdView" ||
          newVal == "policyList" ||
          newVal == "communityHome" ||
          newVal == "communityDetail" ||
          Route.path.includes("cityArea") ||
          Route.path.includes("regionHome")
        ) {
          data.showShoppingCar = false;
        } else {
          data.showShoppingCar = true;
        }
        if (newVal == "communityHome" || newVal == "communityDetail" || newVal == "thirdView") {
          data.showShequ = true;
        } else {
          data.showShequ = false;
        }
      },
      { deep: true, immediate: true }
    );

    const isShowPass = () => {
      if (userInfo.updatePwd === 0) {
        data.resetPass.isShow = true;
        data.resetPass.procedure = 1;
      } else {
        data.resetPass.isShow = false;
        data.resetPass.procedure = 1;
      }
    };
    isShowPass();

    const typeInput = (type) => {
      if (type == "old") data.oldInput = !data.oldInput;
      if (type == "new") data.newInput = !data.newInput;
      if (type == "ok") data.okInput = !data.okInput;
    };

    const submit = () => {
      return false;
      formRef.value
        .validate()
        .then(() => {
          let param = {
            id: userInfo.id,
            newPassword: AES.encrypt(
              "yd@10086qwerty!@",
              "yd@10086qwerty!@",
              data.formData.newPassword
            ),
          };
          resetPwds(param).then((res) => {
            if (res.code === 200) {
              message.success("修改成功");
              logOut();
            }
          });
        })
        .catch(() => {});
    };
    const showMenu = () => {
      getHelp({ type: 1 }).then((res) => {
        if (res.data != null) {
          data.haveBook = true;
        }
      });
      getHelp({ type: 0 }).then((res) => {
        if (res.data != null) {
          data.haveGuide = true;
        }
      });
    };
    // showMenu();
    const toView = (value) => {
      Router.push({
        name: "viewPageMd",
        query: {
          type: 0,
        },
      });
    };
    const isCardClosed = ref(false)
    const closeCard = () =>{
      zIndexBig.value = true;
      if (isCardClosed.value) {
        setTimeout(()=>{
          data.shopCard = true;
          data.shequCard = true;
          zIndexBig.value = false;
          data.borderShow = false;
        }, 200)
        setTimeout(()=>{
          data.borderShow = true;
        }, 80)
        const pos1 = getInitialPosition1()
        const pos2 = getInitialPosition2()
        const pos3 = getInitialPosition3()
        x1.value = pos1.x
        y1.value = pos1.y
        x2.value = pos2.x
        y2.value = pos2.y
        x3.value = pos3.x
        y3.value = pos3.y
      } else {
        setTimeout(()=>{
          data.borderShow = true;
        }, 80)
        setTimeout(()=>{
          data.shopCard = false;
          data.shequCard = false;
          zIndexBig.value = false;
          data.borderShow = false;
        }, 200)
        const rightDockX = window.innerWidth - 30
        x1.value = rightDockX
        x2.value = rightDockX
        x3.value = rightDockX
      }
      isCardClosed.value = !isCardClosed.value
    }
    const toCommunity = () => {
      Router.push({
        name: "communityHome",
      });
    };
    const showThinkingSwitch = () => {

    };
    const handleHeadNameClick = () => {
      data.headNameClickCount = (data.headNameClickCount || 0) + 1;
      if (data.headNameClickCount >= 10) {
        data.headNameClickCount = 0; // Reset count after 10 clicks
        // 使用eventBus触发事件
        eventBus.emit('showThinkingSwitch');
      }
    };
    return {
      ...toRefs(data),
      x1,y1,x2,y2,x3,y3,
      target1,
      target2,
      target3,
      getInitialPosition1,
      getInitialPosition2,
      getInitialPosition3,
      getTitle,
      toManage,
      zIndexBig,
      toCommunity,
      toView,
      leaveChange,
      baseURL,
      closeCard,
      backHome,
      showMenu,
      logOut,
      personInfo,
      cardEnter,
      cardLeave,
      shopLeave,
      shopEnter,
      revampPsd,
      abilityHome,
      showModal,
      closeModal,
      knowClose,
      getReadNotice,
      loginname,
      loginSex,
      counterStore,
      hoverChange,
      menuNavigate,
      Route,
      noticeModal,
      isShowPass,
      submit,
      formRef,
      typeInput,
      goCustomized,
      typeChange,
      toBackendManage,
      showThinkingSwitch,
      handleHeadNameClick,
    };
  },
});
</script>

<style lang="scss" scoped>
.overlayBac {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .tip {
    width: 700px;
    height: 460px;
    border-radius: 8px;
    background-color: #ffffff;

    .banner {
      width: 100%;
      height: 130px;
      background-image: url("@/assets/images/newProject/password.png");
      background-size: 100% 100%;
    }

    .AItitle {
      margin-top: 20px;
      display: flex;
      justify-content: center;

      img {
        width: 40px;
        height: 50px;
        margin-right: 14px;
      }

      .title {
        width: 500px;
        height: 40px;
        border-radius: 0 10px 10px 10px;
        background: linear-gradient(
          90deg,
          rgba(77, 154, 242, 0.2) 0%,
          rgba(53, 112, 242, 0.2) 56%,
          rgba(122, 53, 242, 0.2) 95%
        );
        text-align: center;
        line-height: 40px;
        color: #4249c5;
      }
    }

    :deep(.ant-row) {
      justify-content: center;
    }
  }
}

:deep(.ant-modal-header) {
  padding: 0 !important;
}

/*.shopping {
  position: fixed;
  right: 36px;
  bottom: 9%;
  z-index: 9999;
}*/
.shequ {
  width: 28px;
  height: 28px;
}

.draggable-box1 {
  position: fixed;
  width: 45px;
  height: 90px;
  /*cursor: move;*/
  user-select: none;
  z-index: 999;
  transition: left 0.3s ease, top 0.3s ease;

}

.draggable-box2 {
  position: fixed;
  width: 45px;
  height: 90px;
  /*cursor: move;*/
  user-select: none;
  z-index: 1000;
  transition: left 0.3s ease, top 0.3s ease;
}

.draggable-box3 {
  position: fixed;
  width: 45px;
  height: 90px;
  /*cursor: move;*/
  user-select: none;
  z-index: 990;
  transition: left 0.3s ease, top 0.3s ease;
}

.zIndexBigClass {
  z-index: 1001 !important;
}

.comBtn {
  position: relative;
}
.comBtnShequ {
  cursor: pointer;
	width: 45px;
	height: 90px;
  background: linear-gradient( 360deg, rgba(232,236,255,0.5) 0%, rgba(255,255,255,0.5) 27%, rgba(255,255,255,0.5) 74%, rgba(232,236,255,0.5) 100%);
  border: 1px solid #FFFFFF;
  display: flex;
  align-items: center;
  flex-direction: column;
  img {
    margin-top: 10px;
  }
}
.shequActive {
  background: linear-gradient( 360deg, #E8ECFF 0%, #FFFFFF 27%, #FFFFFF 74%, #E8ECFF 100%);
}
.comBtnShequActive {
  border-radius: 24px 0px 0px 24px;
}
.comActive {
  border-top-left-radius: 24px;
}
.logo {
  margin-left: 16px;
  width: 160px;
  // height: 50px;
}
.comNum {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f5222dff;
  color: #ffffff;
  top: -6px;
  right: -6px;
  text-align: center;
  line-height: 20px;
  font-size: 15px;
}
.prepare {
  font-weight: 500;
  font-size: 14px;
  color: #0C70EB;
  text-align: center;
  line-height: 1.2;
  margin-top: 4px;
}
#tabContainer {
  font-weight: 400;
  font-size: 16px;
  color: #24456a;
  display: flex;
  align-items: center;
  max-width: 100%;
  white-space: nowrap;
  // overflow-x: auto;
  margin-left: 40px;
}

.leftScroll {
  margin-right: 8px;
  color: #24456a;
}

.rightScroll {
  margin-left: 8px;
  color: #24456a;
}

.headAvatar {
  display: flex;
  align-items: center;
  float: right;

  .headName {
    background-color: rgba(12, 112, 235, 0.08);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    color: #0c70eb;
    margin-right: 8px;
    >img{
    	width: 100%;
    	height: 100%;
    }
  }

  .avatarImg {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }

  .ant-dropdown-link {
    color: #24456a;
  }
}

.rightContent {
  width: max-content;
  margin-left: auto;
  height: 100%;
  margin-right: 16px;
}

.sets {
  width: 16px;
  height: 16px;
}

.headTitle {
  margin: 0 24px;
  cursor: pointer;
}

.activeTitle {
  color: #236cff;
}

.tabActive {
  color: #236cff;
}

.lineStyle {
  width: 1px;
  height: 18px;
  background-color: #dbe2ed;
  margin: 0 14px;
}

.layout_top {
  margin: 0 0;
  // margin-right: 33px;
  margin-bottom: 16px;
}

#layout_content {
  min-height: calc(100vh - 60px);
  max-height: calc(100vh - 60px);
  overflow-y: auto;
  // background: rgb(245, 247, 252);
  background-color: #e7f0fa;
  z-index: 900;
  // background-image: url("@/assets/images/newProject/homeBg.png");
  background-size: cover;
  scrollbar-width: auto;
  scrollbar-color: rgba(0, 0, 0, 0.3) #f5f6fa;
}

:deep(.ant-layout-header) {
  padding-inline: 0;
}

.loginBtn {
  button {
    background: rgba(35, 108, 255, 0.1) !important;
    border-radius: 100px;
    font-size: 14px;
    color: #236cff;
    width: 80px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>

<style lang="scss">
.menuText .ant-dropdown-menu {
  width: 88px;
  text-align: center;
}

.menuText .ant-dropdown-menu-item {
  color: rgba(0, 6, 14, 0.6);
}

.menuText .ant-dropdown-menu-item:hover {
  font-weight: 500;
  color: #0c70eb;
}

.navText .ant-dropdown-menu-item {
  color: rgba(0, 6, 14, 0.6);
}

.navText .ant-dropdown-menu-item:hover {
  font-weight: 500;
  color: #0c70eb;
}

.versionModal .ant-modal-body {
  padding: 0 !important;
}

.versionModal .ant-modal-content {
  border-radius: 8px;
}
</style>
<style lang="scss">
.chooseProductModal {
  .ant-modal-body {
    padding: 0;
  }

  .productBanner {
    width: 100%;
    height: 140px;
    background-image: url(../assets/images/layout/productChooseBanner.png);
  }

  .productContent {
    width: 100%;
    height: 300px;

    img {
      width: 140px;
      height: 140px;
      border-radius: 32px;
    }

    .toProductBag {
      .productImg {
        width: 140px;
        height: 140px;
        background-image: url(../assets/images/layout/noChooseProductBag.png);
        background-size: cover;
        background-repeat: no-repeat;
      }

      .title {
        font-weight: 500;
        font-size: 22px;
        color: #24456a;
      }

      .words {
        font-weight: 400;
        font-size: 16px;
        color: rgba(46, 56, 82, 0.65);
      }

      .productImg:hover {
        // border: 4px solid #0C70EB;
        background-image: url(../assets/images/layout/choosedProductBag.png);
      }
    }

    .toMore {
      width: 230px;
      height: 230px;

      .title {
        font-weight: 500;
        font-size: 22px;
        color: rgba(0, 0, 0, 0.45);
      }

      .words {
        font-weight: 400;
        font-size: 16px;
        color: rgba(46, 56, 82, 0.65);
      }
    }
  }
}

.chatHistory {
}
.suspendCard {
  cursor: pointer;
  width: 17px;
  height: 90px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  background: linear-gradient( 90deg, #679FFF 0%, #A794FF 100%);
  border-radius: 24px 0px 0px 24px;
  border: 1px solid #FFFFFF;
  .normal {
    color: rgba(255, 255, 255, 1);
    font-size: 16px;
  }
}
.suspendActive {
  background: linear-gradient( 90deg, #2D7AFF 0%, #8469FF 100%);
}
.cardActive {
  height: 180px;
}

.workOrder {
  padding: 10px;
  display: flex;
  align-items: center;
  .workOrder_image {
    width: 36px;
    height: 36px;
    background-image: url(../assets/images/home/<USER>
    margin-right: 8px;
  }
  .workOrder_body {
    text-align: left;
    width: calc(100% - 134px);
    align-content: center;
    .workOrder_body_title {
      width: 100%;
      font-weight: 500;
      font-size: 14px;
      color: #24456a;
      line-height: 14px;
      margin-bottom: 4px;
      white-space: nowrap; /* 强制文本不换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 溢出时显示省略号 */
    }
    .workOrder_body_time {
      font-weight: 400;
      font-size: 12px;
      color: #00000073;
      line-height: 12px;
      margin-bottom: 0;
    }
  }
  .workOrder_type {
    width: 90px;
  }
}
</style>
